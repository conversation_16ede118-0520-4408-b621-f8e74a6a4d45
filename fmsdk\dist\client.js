(()=>{var i=2e4,d=0;function a(e,t){return new Promise(r=>{let s=!1,u=setTimeout(()=>{s=!0,r({success:!1,errorMsg:`Server RPC "${e}" has timed out after ${i}ms`})},i),n=`${e}:${d++}-${Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(36)}`;emitNet(e,n,t);function o(c){removeEventListener(n,o),!s&&(clearTimeout(u),r(c))}onNet(n,o)})}async function f(e){let t=await a("fivemanage:takeImage",e);if(t.success===!1)throw new Error(t.errorMsg);return t.data}function g(){exports("takeImage",f)}function m(){g()}function l(){m()}l();})();

{"PHONE_SETTINGS_NUMBERFORMAT": "en-US", "PHONE_SETTINGS_DATEFORMAT": "en-US", "PHONE": {"PHONE_SCREEN_LOADING": "Loading...", "PHONE_HOMESCREEN_NOTIFY_CENTRE": "Notification Center", "PHONE_HOMESCREEN_NOTIFY_SHOWLESS": "Show Less", "PHONE_PASSCODE_ENTERPASSCODE": "Enter Password", "PHONE_PASSCODE_CANCEL": "Cancel", "PHONE_DISABLED": "This phone usage has been disabled by its owner.", "PHONE_STATUSBAR_NOTIFY_CALLING": "Mobile", "PHONE_STATUSBAR_NOTIFY_VIDMEET": "<PERSON>id<PERSON><PERSON><PERSON>", "PHONE_STATUSBAR_NOTIFY_CANCEL": "Cancel", "PHONE_STATUSBAR_NOTIFY_OK": "Ok", "PHONE_STATUSBAR_DONE": "Done", "PHONE_STATUSBAR_CLOSE": "Close", "PHONE_STATUSBAR_ADDPAGE": "Add Page", "PHONE_SET_EYETARGET": "Share Number", "PHONE_IMAGE_COPYLINK": "Image URL Copied", "PHONE_PIN_ERORR": "Error", "PHONE_PIN_SUCCESSFUL": "Successful", "PHONE_PIN_ACCOUNTCHECK": "Checking Please Wait", "PHONE_PIN_EMAILPASSWORD_BLANK": "Email or password can't be blank", "PHONE_PIN_EMAILPASSWORD_INCORRECT": "Email or password is incorrect", "PHONE_PIN_INFO": "Your password is {pin}", "PHONE_PASSCODE_FORGOT": "Forgot Password", "PHONE_PHONEBOX": "Phone booth", "PHONE_SIGNAL_LOW": "The signal is weak in your area", "PHONE_SIGNAL_LOW_TITLE": "Weak Signal", "PHONE_EDIT_MODE_TITLE": "Edit Mode", "PHONE_EDIT_MODE_DESC": "When you delete the application, it is only deleted from the page, not removed."}, "Charge": {"APP_CHARGE_ALREADY": "Your phone is already charging", "APP_CHARGE_NOTIFY": "Your phone is charging", "APP_CHARGE_NORMALSTATION": "Your phone is in the normal charging station", "APP_CHARGE_FASTSTATION": "Your phone is in the fast charging station", "APP_CHARGE_TOOK_PHONE": "You took the phone from the charging station", "APP_CHARGE_PICK_UP_PHONECHARGE": "You can pick up the phone from the charging station", "APP_CHARGE_NORMAL_CHARGE": "Normal Charging", "APP_CHARGE_FAST_CHARGE": "Fast Charging", "APP_CHARGE_CANCEL": "Cancel Charge", "APP_CHARGE_BANK_MONEY": "You don't have enough money"}, "DateAgo": {"minutesAgo": "{minutes} minutes ago", "hoursAgo": "{hours} hours ago", "justNow": "now"}, "CameraAPP": {"APP_CAMERA_PHOTO": "Photo", "APP_CAMERA_VIDEO": "Video", "APP_CAMERA_LANDSCAPE": "Landscape", "APP_CAMERA_LOADING": "Loading", "InfoPanel": {"APP_CAMERA_INFO_ROTATE": "Rotate the camera", "APP_CAMERA_INFO_MOVING": "You can move the camera by holding down the right mouse button", "APP_CAMERA_INFO_FACIALCHANGE": "Change facial expression", "APP_CAMERA_INFO_ANIMATION": "Use animation", "APP_CAMERA_INFO_ANIMATIONCHANGE": "Change animation"}}, "ModalNumber": {"MODAL_SEND": "Send", "MODAL_CANCEL": "Cancel", "MODAL_OK": "OK"}, "ModalAirDrop": {"MODAL_AIRDROP_TITLE": "AirShare", "MODAL_AIRDROP_SEND": "Send!", "MODAL_AIRDROP_NUMBER_SUCCESS": "Your phone number has been shared", "MODAL_AIRDROP_CONTACTSAVED": "{name} was registered in the contacts", "MODAL_AIRDROP_CONTACTSEND": "{name} shared his phone number with you.", "MODAL_AIRDROP_CONTACTALREADYSAVED": "This person is already saved in your contacts", "MODAL_AIRDROP_COPY_IMAGE_LINK": "Copy Image Link", "MODAL_AIRDROP_COPY_VIDEO_LINK": "Copy Video Link", "MODAL_AIRDROP_USE_WALLPAPER": "Use as Wallpaper", "MODAL_AIRDROP_SHARED_IMAGE": "Shared a {type} with you", "MODAL_AIRDROP_IMAGEVIDEO_SUCCESS": "The {type} has been shared successfully", "MODAL_AIRDROP_COPIED": "Successfully copied to clipboard", "MODAL_AIRDROP_WALLPAPER_CHANGED": "Wallpaper changed successfully"}, "SettingAPP": {"APP_SETTING_TITLE": "Settings", "APP_SETTING_GENERAL": "General", "APP_SETTING_STREAMER_MODE": "Streamer Mode", "APP_SETTING_AIRPLANE_MODE": "Airplane Mode", "APP_SETTING_SOUND": "Sound", "APP_SETTING_WALLPAPER": "Wallpapers", "APP_SETTING_NOTIFICATIONS": "Notifications", "APP_SETTING_BORDER_COLOR": "Border Color", "APP_SETTING_DARK_MODE": "Dark Mode", "APP_SETTING_RESET_PHONE": "Reset Phone", "APP_SETTING_ABOUT": "About", "APP_SETTING_LOOKID": "Look ID", "APP_SETTING_PASSWORD": "Password", "APP_SETTING_CHANGEPASSWORD_POUP_TITLE": "Change Password", "APP_SETTING_CHANGEPASSWORD_POUP_DESC": "Are you sure you want to change your password?", "APP_SETTING_CHANGEPASSWORD_ENTERPASSCODE": "Enter Password", "APP_SETTING_CHANGEPASSWORD_CANCEL": "Cancel", "APP_SETTING_CHANGEPASSWORD_PASSCODECHANGED": "Password Changed", "APP_SETTING_CHANGEPASSWORD_PASSCODEFAILED": "Password Couldn't Changed", "APP_SETTING_PHONE_RESET": "Reset Phone", "APP_SETTING_PHONE_RESETDESC": "Are you sure you want to reset the phone?", "APP_SETTING_PHONE_RESETSUCESS": "Your phone has been reset", "APP_SETTING_PHONENAME": "Phone Name", "APP_SETTING_SOFTWAREVERSION": "Software Version", "APP_SETTING_PHONEVERSION": "Phone Version", "APP_SETTING_PHONEUPTODATE": "Your phone is up to date", "APP_SETTING_PHONEUPDATE": "Please update your phone. Contact your server owner.", "APP_SETTING_PHONE_RESETLOADING": "Password is being reset...", "APP_SETTING_PHONE_RESETERROR": "Password couldn't reset", "APP_SETTING_PHONESERIALNUMBER": "Serial number", "APP_SETTING_REPORTBUG": "Report Bug", "APP_SETTING_BUGREPORTLABEL": "Bug Report", "APP_SETTING_BUGREPORTDESC": "Please write your bug report", "APP_SETTING_BUGREPORTSEND": "Send", "APP_SETTING_BUGREPORTCANCEL": "Cancel", "APP_SETTING_BUGREPORTSUCCESS": "Your bug report has been sent", "APP_SETTING_BUGREPORTEMPTY": "You can't send an empty bug report", "APP_SETTING_FEEDBACK": "<PERSON><PERSON><PERSON>", "APP_SETTING_FEEDBACKLABEL": "<PERSON><PERSON><PERSON>", "APP_SETTING_FEEDBACKDESC": "Please write your feedback", "APP_SETTING_FEEDBACKSEND": "Send", "APP_SETTING_FEEDBACKCANCEL": "Cancel", "APP_SETTING_FEEDBACKSUCCESS": "Your feedback has been sent", "APP_SETTING_FEEDBACKEMPTY": "You can't send an empty feedback", "APP_SETTING_CHANGLOG": "Changelog", "APP_SETTING_CHANGLOGPOUPCLOSE": "Close", "APP_SETTING_CUSTOMEWALLPAPER": "Custom Wallpaper", "APP_SETTING_CUSTOMEWALLPAPERDESC": "Enter the URL of the image you want to use as wallpaper", "APP_SETTING_LANG": "Language", "APP_SETTING_NAMECHANGELOADING": "Changing name...", "APP_SETTING_NAMECHANGEDESC": "Specify your name to be used in some apps and functions", "APP_SETTING_NAMECHANGETITLE": "Change Phone Name", "APP_SETTING_NAMECHANGESUCESS": "Your phone name has been changed", "APP_SETTING_QRCODEREALAPP": "Log in with QR", "APP_SETTING_QRCODE_SECRET_DESC": "This QR code authenticates your phone. Do not share this code with anyone.", "APP_SETTING_NAMECHANGEERROR_ALERT_LENGHT_MIN": "Your name must be at least 4 characters", "APP_SETTING_NAMECHANGEERROR_ALERT_LENGHT_MAX": "Your name must be no more than 20 characters", "APP_SETTING_LUCY": "<PERSON>"}, "ContactsAPP": {"APP_CONTACTS_TITLE": "Contacts", "APP_CONTACT_MYCARD": "My Card", "APP_POUP_CLOSE": "Close", "APP_POUP_DONE": "Done", "APP_CONTACT_ADD_PHOTO": "Add Photo", "APP_CONTACT_NAME_PLACEHOLDER": "Enter name", "APP_CONTACT_NUMBER_PLACEHOLDER": "Enter number", "APP_CONTACT_BACK": "Back", "APP_CONTACT_EDIT": "Edit", "APP_CONTACT_DECLINE": "Decline", "APP_CONTACT_ACCEPT": "Accept", "APP_CONTACT_CHOOSE_YOUR_POSTER": "Choose <PERSON>", "APP_CONTACT_CAMERA": "Camera", "APP_CONTACT_PHOTOS": "Photos", "APP_CONTACT_MONOGRAM": "Monogram", "APP_CONTACT_CONTINUE": "Continue", "APP_CONTACT_BACKGROUND_COLOR": "Background Color", "APP_CONTACT_COLOR": "Color", "APP_CONTACT_GALLERY": "Gallery", "APP_CONTACT_TUNEFIY": "Tuneify", "APP_CONTACT_NO_WALLPAPER": "No wallpapers found", "APP_CONTACT_MESSAGE": "message", "APP_CONTACT_CALL": "call", "APP_CONTACT_VIDEO_CALL": "vid<PERSON>t", "APP_CONTACT_SHARE": "share", "APP_CONTACT_DELETE_CONTACT": "Delete Contact", "APP_CONTACT_BLOCK": "Block Contact", "APP_CONTACT_UNBLOCK": "Unblock Contact", "APP_CONTACT_ADDTO_FAVORITES": "Add to Favorites", "APP_CONTACT_REMOVEFROM_FAVORITES": "Remove from Favorites", "APP_CONTACT_NOTSAVE": "Person couldn't be registered", "APP_CONTACT_DELETING": "Deleting", "APP_CONTACT_BLOCKING": "Blocking", "APP_CONTACT_UNBLOCKING": "Unblocking", "APP_CONTACT_COPY_NUMBER": "Copy Number", "APP_CONTACT_LOADING": "Loading", "APP_CONTACT_SAVED": "Contact saved successfully", "APP_CONTACT_AVATAR_CHANGE": "Change Avatar", "APP_CONTACT_UPDATESUCESS": "Updated successfully", "APP_CONTACT_SAVESUCESS": "Saved successfully", "APP_CONTACT_DELETESUCESS": "Deleted successfully", "APP_CONTACT_BLOCKSUCESS": "Blocked successfully", "APP_CONTACT_UNBLOCKSUCESS": "Unblocked successfully", "APP_CONTACT_BLOCKERROR": "An error occurred while blocking", "APP_CONTACT_RINGTONE": "Ringtone", "APP_CONTACT_PHONE": "phone", "APP_CONTACT_PHOTO_AND_POSTER": "Person Photo and Poster"}, "CallAPP": {"APP_CALL_CALLINGMOBILE": "calling mobile...", "APP_CALL_WAITING": "Waiting for Others", "APP_CALL_SPEAKER": "Speaker", "APP_CALL_VIDMEET": "<PERSON>id<PERSON><PERSON><PERSON>", "APP_CALL_MUTE": "Mute", "APP_CALL_DECLINE": "Decline", "APP_CALL_ADD": "Add", "APP_CALL_ACCEPT": "Accept", "APP_CALL_END": "End", "APP_CALL_JOIN": "Join", "APP_FAVORITES_EDIT": "Edit", "APP_FAVORITES_MOBILE": "mobile", "APP_FAVORITES_CANCEL": "Cancel", "APP_FAVORITES_SELECTCONTACT": "Select Contact", "APP_FAVORITES_SEARCH_CANCEL": "Cancel", "APP_FAVORITES_SEARCH_PLACEHOLDER": "Search", "APP_RECENTS_CLEARRECENT_TITLE": "Clear", "APP_RECENTS_MOBILE": "Mobile", "APP_RECENTS_VIDMEET": "Video Call", "APP_RECENTS_CALL": "Call", "APP_RECENTS_LOADING_CLEARING": "Clearing", "APP_RECENTS_CLEARRECENT": "Recent calls cleared", "APP_RECENTS_NOTCLEARRECENT": "An error occurred while clearing recent calls.", "APP_RECENTS_CLEARALL": "All Recents Cleared", "APP_RECENTS_FULLCLEAR_CONFIRM_TITLE": "Clear All Recents", "APP_RECENTS_FULLCLEAR_CONFIRM_DESC": "Are you sure you want to clear all recent calls?", "APP_RECENTS_NEWCONTACT": "New Contact", "APP_RECENTS_NEWCONTECT_NAME": "Write name to save the contact", "APP_RECENTS_NEWCONTECT_LOADING": "Saving contact", "APP_RECENTS_NEWCONTACT_SAVESUCCESS": "Contact saved successfully", "APP_RECENTS_NEWCONTACT_NOTSAVE": "An error occurred while saving the contact.", "APP_CALL_TABBAR_FAVORITES": "Favorites", "APP_CALL_TABBAR_RECENTS": "Recents", "APP_CALL_TABBAR_KEYPAD": "Keypad", "APP_FAVORITES_TITLE": "Favorites", "APP_VIDMEET_JOINQUEST": "Do you want to join?", "APP_CALL_PRIVATENUMBER": "Private Number", "APP_CALL_VIDMEET_SENDERACCEPT": "Do you want to switch to VidMeet calling?", "APP_CALL_VIDMEET_ACCEPT": "Expected to accept VidMeet calls", "APP_CALL_VIDMEET_REJECT": "No answer", "APP_CALL_VIDMEET_JOINEDASK": "You have been added to the call", "APP_CALL_GROUPCALL": "Group Call", "APP_CALL_RECENT_NOTIFY": "Missed call"}, "MessageAPP": {"APP_MESSAGE_TITLE": "Message", "APP_MESSAGE_CANCEL": "Cancel", "APP_MESSAGE_CREATEGROUP": "Create Group", "APP_MESSAGE_CREATE": "Create", "APP_MESSAGE_GROUPAVATAR": "Group Avatar", "APP_MESSAGE_GROUPAVATARLINK": "Photo Link", "APP_MESSAGE_GROUPNAMELABEL": "Group Name", "APP_MESSAGE_GROUPNAMEPLACEHOLDER": "Enter group name", "APP_MESSAGE_GROUPABOUTLABEL": "About", "APP_MESSAGE_GROUPABOUTPLACEHOLDER": "Enter about", "APP_MESSAGE_MEMBER": "Member", "APP_MESSAGE_MEMBERPLACEHOLDER": "New Member Add ", "APP_MESSAGE_CHANGEAVATAR": "Change Avatar", "APP_MESSAGE_CHANGEAVATARDESC": "Are you sure you want to change the avatar?", "APP_MESSAGE_CHANGEDAVATAR": "Avatar changed.", "APP_MESSAGE_SAVECONTACT": "Save Contact", "APP_MESSAGE_GIF": "GIF", "APP_MESSAGE_SEARCHBAR_PLACEHOLDER": "Search", "APP_MESSAGE_NOTSENDMESSAGE": "An error occurred while sending the message.", "APP_MESSAGE_LOADERSAVECONTACT": "Loading, please wait", "APP_MESSAGE_SUCCESSAVECONTACT": "Contact saved successfully", "APP_MESSAGE_NOTSAVECONTACT": "An error occurred while saving the contact.", "APP_MESSAGE_LOADERSAVINGGROUP": "Saving group", "APP_MESSAGE_GROUPUPDATE": "Group was updated successfully.", "APP_MESSAGE_OPTIONS": "Options", "APP_MESSAGE_DELETE": "Delete", "APP_MESSAGE_LOADERDELETEMESSAGE": "Loading, please wait", "APP_MESSAGE_DELETEMESSAGESUCCESS": "Message deleted successfully.", "APP_MESSAGE_DELETEMESSAGENOT": "An error occurred while deleting the message.", "APP_MESSAGE_CONTACTNAME": "Contact Name", "APP_MESSAGE_DELETEGROUP": "Delete Group", "APP_MESSAGE_GROUPDELETESUCCESS": "Group was deleted successfully.", "APP_MESSAGE_GROUPDELETENOT": "An error occurred while deleting the group.", "APP_MESSAGE_GROUPLEAVE": "Leave Group", "APP_MESSAGE_GROUPLEAVESUCCESS": "You have left the group.", "APP_MESSAGE_GROUPLEAVENOT": "An error occurred while leaving the group.", "APP_MESSAGE_ERROR_GROUPNAME": "Group name must be greater than 4 characters", "APP_MESSAGE_ERROR_GROUPABOUT": "Group about must be greater than 4 characters", "APP_MESSAGE_ERROR_GROUPAVATAR": "You must add a group avatar", "APP_MESSAGE_ERROR_GROUPMEMBER": "You must add at least two member", "APP_MESSAGE_LOADERCREATEGROUP": "Creating group", "APP_MESSAGE_GROUPCREATESUCCESS": "Group created successfully", "APP_MESSAGE_GROUPCREATENOT": "An error occurred while creating the group.", "APP_MESSAGE_GROUPREMOVEDMEMBER_SUCESS": "You have been removed from the group", "APP_MESSAGE_NONAME": "No name", "APP_MESSAGE_NOMESSAGE": "No message", "APP_MESSAGE_TIME_MONTHAGO": "{months} months ago", "APP_MESSAGE_TIME_DAYAGO": "{days} days ago", "APP_MESSAGE_TIME_HOURAGO": "{hours} hours ago", "APP_MESSAGE_TIME_MINAGO": "{minutes} minutes ago", "APP_MESSAGE_TIME_SECAGO": "{seconds} seconds ago", "APP_MESSAGE_TIME_JUSTNOW": "now", "APP_MESSAGE_NOTIFY_GROUPMESSAGE": "{groupname} group received a message", "APP_MESSAGE_LOCATION": "A GPS was shared", "APP_MESSAGE_IMAGE": "An image was shared", "APP_MESSAGE_VIDEO": "A video was shared", "APP_MESSAGE_AUDIO": "An audio was shared", "APP_MESSAGE_CONTACT": "A contact was shared", "APP_MESSAGE_PLACEHOLDER": "Type a message", "APP_MESSAGE_OPTION_CANCEL": "Cancel", "APP_MESSAGE_MODAL_CANCEL": "Cancel", "APP_MESSAGE_MODAL_SEARCH": "Search", "APP_MESSAGE_ISTYPING": "{name} is typing...", "APP_MESSAGE_LIVEGPS_TACKLOCATION": "Track Live Location", "APP_MESSAGE_LIVEGPS_EXPIRED": "Expired", "APP_MESSAGE_LIVEGPS_TITLE": "Send Live Location", "APP_MESSAGE_GPS_TITLE": "Send Location", "APP_MESSAGE_BLIP_LIVE": "Live Location", "APP_MESSAGE_LIVEGPS": "Live location shared", "APP_MESSAGE_GROUP_UPDATE": "Update", "APP_MESSAGE_GROUP_UPDATE_PRELOAD": "Updating", "APP_MESSAGE_GROUP_AVATAR_DESC_LINK": "Enter an image URL for the group picture", "APP_MESSAGE_GROUP_MEMBER_DELETE": "Remove Member", "APP_MESSAGE_GROUP_MEMBER_YOU": "You", "APP_MESSAGE_GROUP_MEMBER_CALL": "Call", "APP_MESSAGE_NOTIFY_AUDIO": "Sent a voice message", "APP_MESSAGE_NOTIFY_CONTACT": "Sent a contact card", "APP_MESSAGE_NOTIFY_IMAGE": "Sent an image", "APP_MESSAGE_NOTIFY_LOCATION": "Sent a location", "APP_MESSAGE_NOTIFY_LIVELOCATION": "Sent a live location", "APP_MESSAGE_NOTIFY_VIDEO": "Sent a video", "APP_MESSAGE_SEND_PHOTO_LINK": "Send image via link", "APP_MESSAGE_SEND_PHOTO_LINK_DESC": "Enter the image URL", "APP_MESSAGE_SEND_PHOTO_LINK_ERROR": "Invalid image URL", "APP_MESSAGE_SEND_PHOTO_GALLERY": "Send image from gallery", "APP_MESSAGE_SEND_PHOTO_CAMERA": "Take photo", "APP_MESSAGE_SHARED_SEND": "Send", "APP_MESSAGE_SHAREMESSAGE": "Share Message", "APP_MESSAGE_SHAREMESSAGECONFIRM": "Are you sure you want to share this message?", "APP_MESSAGE_GROUPDELETECONFIRM": "Are you sure you want to delete this group?", "APP_MESSAGE_GROUPLEAVECONFIRM": "Are you sure you want to leave this group?", "APP_MESSAGE_GROUPDELETE": "Delete Group", "APP_MESSAGE_BLOCK_NUMER": "You are blocked by this number", "APP_MESSAGE_GROUP_ADD_MEMBER_ERROR": "You cannot add the person to the group", "APP_MESSAGE_GROUP_REMOVE_MEMBER_ERROR": "Person could not leave the group", "APP_MESSAGE_SAVE_CONTACT_ERROR_YOU_SELF": "You cannot save yourself"}, "SetupScreen": {"PHONE_SETUP_HELLO": "Hello", "PHONE_SETUP_CLIKUP": "Click to set up your phone", "SettingLangueage": "Setting language...", "PHONE_SETUP_LOGINGKSID": "GKS ID", "PHONE_SETUP_LOGINGKSIDDETAIL": "Log in your phone account. If you have an phone account, you can access your phone data in cloud.", "PHONE_SETUP_LOGINGKSIDPLACEHOLDER": "Email", "PHONE_SETUP_LOGINPASSWORD": "Password", "PHONE_SETUP_LOGINPASSWORDPLACEHOLDER": "Enter Password", "PHONE_SETUP_LOGINLOGIN": "<PERSON><PERSON>", "PHONE_SETUP_LOGINLOADING": "Please wait while your login is being verified", "PHONE_SETUP_LOGINERROR": "Email or password is incorrect", "PHONE_SETUP_LOGINFORGOTPASSWORD": "Forgot Password?", "PHONE_SETUP_LOGINCREATEACCOUNT": "Create Account", "PHONE_SETUP_FORGOTCANCEL": "Cancel", "PHONE_SETUP_FORGOTSUBMIT": "Submit", "PHONE_SETUP_FORGOTTITLE": "Forgot Password", "PHONE_SETUP_FORGOTSECURITYQUESTION": "Security Question", "PHONE_SETUP_FORGOTSECURITYANSWER": "Security Answer", "PHONE_SETUP_FORGOTSECURITYPLACEHOLDER": "Write security answer", "PHONE_SETUP_FORGOTPASSWORDTITLE": "Reset Password", "PHONE_SETUP_FORGOTPASSWORDSUBMIT": "Submit", "PHONE_SETUP_FORGOTPASSWORDNEW": "New Password", "PHONE_SETUP_FORGOTPASSWORDNEWREPEAT": "Repeat New Password", "PHONE_SETUP_FORGOTPASSWORDNEWREPEATPLACEHOLDER": "Repeat new password", "PHONE_SETUP_CREATEACCOUNT": "Create Account", "PHONE_SETUP_CREATEACCOUNTDETAIL": "Create a new phone account. Your phone data will be saved to the account you created.", "PHONE_SETUP_CREATEACCOUNTGKSID": "GKS ID", "PHONE_SETUP_CREATEACCOUNTGKSIDPLACEHOLDER": "Email", "PHONE_SETUP_CREATEACCOUNTPASSWORD": "Password", "PHONE_SETUP_CREATEACCOUNTPASSWORDPLACEHOLDER": "Enter password", "PHONE_SETUP_CREATEACCOUNTREPEATPASSWORD": "Repeat Password", "PHONE_SETUP_CREATEACCOUNTREPEATPASSWORDPLACEHOLDER": "Repeat password", "PHONE_SETUP_CREATEACCOUNTSECURITYQUESTION": "Security Question", "PHONE_SETUP_CREATEACCOUNTSECURITYANSWER": "Security Answer", "PHONE_SETUP_CREATEACCOUNTSECURITYPLACEHOLDER": "Write security answer", "PHONE_SETUP_CREATEACCOUNTCREATE": "Create", "PHONE_SETUP_CREATEACCOUNTERROR": "Passwords don't match", "PHONE_SETUP_CREATEACCOUNTERROR2": "You can't use special characters (Only use > 5)", "PHONE_SETUP_CREATEACCOUNTLOADING": "Please wait while your account is being created", "PHONE_SETUP_CREATEACCOUNTERROR3": "This email address is already in use", "PHONE_SETUP_CREATEACCOUNTERROR1": "The security question cannot be blank", "PHONE_SETUP_CREATEACCOUNTERROR4": "Must be greater than 4 characters", "PHONE_SETUP_APPSDATA": "Apps & Data", "PHONE_SETUP_APPSDATADETAIL": "Restore your phone from a backup or set up as new.", "PHONE_SETUP_APPSDATARESTORE": "Restore from Cloud Backup", "PHONE_SETUP_ESIMSETUPLOADING": "Reloading apps and data please wait...", "PHONE_SETUP_PHONECREATEPASSWORD": "Create Password", "PHONE_SETUP_PHONECREATEPASSWORDDETAIL": "Create a home screen password to protect your phone data.", "PHONE_SETUP_LOOKID": "Look ID", "PHONE_SETUP_LOOKIDDETAIL": "If you set up look id, the phone will be unlocked automatically as soon as it recognizes your face.", "PHONE_SETUP_LOOKIDCONTINUE": "Enable Look ID", "PHONE_SETUP_LOOKIDNOTNOW": "Not Now", "PHONE_SETUP_APPERANCE": "Apperance", "PHONE_SETUP_APPERANCEDETAIL": "Choose a look for your phone.", "PHONE_SETUP_APPERANCELIGHT": "Light", "PHONE_SETUP_APPERANCEDARK": "Dark", "PHONE_SETUP_APPERANCECONTINUE": "Continue", "PHONE_SETUP_FINISHWELCOME": "Welcome to CR Phone", "PHONE_SETUP_FINISHGETSTARTED": "Click to get started", "PHONE_SETUP_FINISH_ERROR": "The phone could not be installed", "PHONE_SETUP_CHANGE_LANGUAGE_ERROR": "Error changing language", "PHONE_SETUP_FINISHNOTIFICATION": "Phone being activated, please wait...", "PHONE_SETUP_PASSWORDRESET_SECURITYSUCCESS": "Security question answered correctly, you can reset your password.", "PHONE_SETUP_PASSWORDRESET_SECURITYANSWEERROR": "Security question answered incorrectly.", "PHONE_SETUP_PASSWORDRESET_SECURITYQUESTERROR": "Security question is wrong.", "PHONE_SETUP_PASSWORDRESET_SECURITYERROR": "An error occurred while validating the security question.", "PHONE_SETUP_PASSWORDRESET_SUCESS": "Your password has been successfully reset.", "PHONE_SETUP_PASSWORDRESET_ERROR": "An error occurred while resetting your password.", "PHONE_SETUP_PASSWORDREFRESHLOADING": "Checking, please wait", "SecurityQuestion": {"question1": "What is your mother's maiden name?", "question2": "What is the name of your favorite pet?", "question3": "What high school did you attend?", "question4": "What was the make of your first car?", "question5": "What was your favorite food as a child?", "question6": "What was the name of your elementary school?", "question7": "What year was your father (or mother) born?", "question8": "In what city were you born?", "question9": "Where did you meet your spouse?"}}, "ServicesAPP": {"APP_SERVICES_TITLE": "Services", "APP_SERVICES_OPEN": "Open", "APP_SERVICES_CLOSE": "Close", "APP_SERVICES_LOCATIONSET": "The location marked on the map", "APP_SERVICES_MESSAGE_TITLE": "Message", "APP_SERVICES_MESSAGE_SENDMESSAGELOADING": "Sending message...", "APP_SERVICES_MESSAGE_SENDMESSAGEERROR": "Failed to send message", "APP_SERVICES_REPORT_TITLE": "Report Message", "APP_SERVICES_REPORT_IMAGEURLPLACEHOLDER": "Image URL", "APP_SERVICES_REPORT_SEND": "Send", "APP_SERVICES_REPORT_SENDANONYMOUS": "Send Anonymous", "APP_SERVICES_REPORT_CANCEL": "Cancel", "APP_SERVICES_REPORT_SENDREPORTLOADING": "Sending report...", "APP_SERVICES_REPORT_REPORTSENT": "Report sent!", "APP_SERVICES_REPORT_ERRORREPORT": "Report error!", "APP_SERVICES_REPORT_REPORTS": "Reports", "APP_SERVICES_REPORT_MESSAGES": "Messages", "APP_SERVICES_REPORT_DELETEALL": "Delete All", "APP_SERVICES_REPORT_SENDER": "Sender", "APP_SERVICES_REPORT_SENDERANONYMOUS": "Anonymous", "APP_SERVICES_REPORT_MESSAGE": "Message", "APP_SERVICES_REPORT_LOCATION": "Location", "APP_SERVICES_REPORT_PHONE": "Phone", "APP_SERVICES_REPORT_TIME": "Time", "APP_SERVICES_REPORT_OPEN": "Open", "APP_SERVICES_REPORT_CLOSE": "Close", "APP_SERVICES_REPORT_CALL": "Call", "APP_SERVICES_REPORT_LOCATIONBUTTON": "Location", "APP_SERVICES_REPORT_MESSAGEBUTTON": "Message", "APP_SERVICES_REPORT_REPORTREVIEWED": "This report is being reviewed by someone else.", "APP_SERVICES_REPORT_NOREPORT": "There are no reports here.", "APP_SERVICES_REPORT_ALLDELETEDESC": "Are you sure you want to delete all reports?", "APP_SERVICES_REPORT_DELETE": "Delete", "APP_SERVICES_REPORT_CANCELBUTTON": "Cancel", "APP_SERVICES_REPORT_NOREPORTTWO": "There are no messages here.", "APP_SERVICES_REPORT_REVIEW": "Review", "APP_SERVICES_REPORT_CLOSEBUTTON": "Close", "APP_SERVICES_REPORT_CLOSEDBUTTON": "Closed", "APP_SERVICES_REPORT_LOADINGPLEASEWAIT": "Loading please wait...", "APP_SERVICES_REPORT_NOTUPDATED": "Report couldn't be updated.", "APP_SERVICES_REPORT_LOCATIONMARK": "Location marked", "APP_SERVICES_NOTIFY_MESSAGE": "Message from Services", "APP_SERVICES_NOTIFY_REPORT": "Report from Services", "APP_SERVICES_NOTIFY_YOURREPORT": "Your report is currently under review...", "APP_SERVICES_REPORT_MESSAGEEMPTY": "You can't send an empty message", "APP_SERVICES_REPORT_STATUS": "Status", "APP_SERVICES_REPORT_NOSEND": "You have a report. You can't send a second report until the old report is closed.", "APP_SERVICES_REPORT_ACTIONS": "Actions", "APP_SERVICES_REPORT_ACTIONS_SETTINGS": "Settings", "APP_SERVICES_REPORT_ACTIONS_ONOFF": "On/Off", "APP_SERVICES_REPORT_ACTIONS_ONOFF_DESC": "Toggle availability via the app", "APP_SERVICES_REPORT_ACTIONS_DUTY": "Duty", "APP_SERVICES_REPORT_ACTIONS_DUTY_DESC": "Indicate whether you are on duty", "APP_SERVICES_REPORT_ACTIONS_CALLS": "Work Calls", "APP_SERVICES_REPORT_ACTIONS_CALLS_DESC": "Toggle work calls on/off", "APP_SERVICES_JOB_BALANCE_TITLE": "Job Balance", "APP_SERVICES_JOB_BALANCE": "Balance", "APP_SERVICES_JOB_BALANCE_DESC": "Current balance of the company", "APP_SERVICES_MANAGE_EMPLOYEES_TITLE": "Manage Employees", "APP_SERVICES_EMPLOYEES_HIRE_NEW": "Hire a new employee for your business.", "APP_SERVICES_EMPLOYESS_HIRE": "<PERSON>re Employee", "APP_SERVICES_NO_JOB_GRADES": "No job grades available", "APP_SERVICES_MANAGE_EMPLOYEES_CHANGERANK_CONFIRM": "Are you sure you want to change {name} to {grade}?", "APP_SERVICES_MANAGE_EMPLOYESS_CLOSE": "Close", "APP_SERVICES_MANAGE_EMPLOYEES_FIRE_DESC": "Are you sure you want to fire {name}?", "APP_SERVICES_MANAGE_EMPLOYESS_FIRE_PRELOADER": "He is being laid off...", "APP_SERVICES_MANAGE_EMPLOYESS_TIP_SEND": "Send money", "APP_SERVICES_MANAGE_EMPLOYESS_HOW_MUCH_TIP": "How much money do you want to send to {name}?", "APP_SERVICES_MANAGE_EMPLOYEES_FIRE": "Fire", "APP_SERVICES_MANAGE_EMPLOYESS_CHANGEGRADE": "Change Grade", "APP_SERVICES_MANAGE_EMPLOYEES_CALL": "Call", "APP_SERVICES_NOTIFY_TIPSUCESS": "<PERSON><PERSON> sent successfully", "APP_SERVICES_NOPLAYER": "{noplayer} player not found", "APP_SERVICES_ACCEPTJOB": "Do you want to accept the hiring for the {job} position?", "APP_SERVICES_NOTIFY_NEWJOB": "You've been hired for a new job", "APP_SERVICES_HIRE_SUCCESS": "You have successfully hired the person", "APP_SERVICES_NOTIFY_TIP": "You have received a tip", "APP_SERVICES_NOTIFY_TIPFAILED": "An error occurred while sending the tip", "APP_SERVICES_MANAGE_EMPLOYEES_FIRE_PLAYER": "You were fired from your job", "APP_SERVICES_MANAGE_EMPLOYEES_FIRE_SUCESS": "You have successfully fired the employee", "APP_SERVICES_MANAGE_EMPLOYEES_FIRE_FAILED": "An error occurred while firing the employee", "APP_SERVICES_MANAGE_EMPLOYEES_CHANGEGRADE_SUCESS": "You have successfully changed the employee's grade", "APP_SERVICES_MANAGE_EMPLOYEES_CHANGEGRADE_FAILED": "An error occurred while changing the employee's grade", "APP_SERVICES_REPORT_REPORT": "Report", "APP_SERVICES_REPORT_REPORTNO": "No one is interested in Report yet"}, "StreamMode": {"APP_STREAMMODE_IMAGE": "Image hidden because Streamer Mode is active"}, "Squawk": {"APP_SQUAWK_TITLE": "Squawk", "APP_SQUAWK_OK": "OK", "APP_SQUAWK_CANCEL": "Cancel", "APP_SQUAWK_LOADING": "Loading...", "APP_SQUAWK_REGISTERING": "Registering...", "APP_SQUAWK_USERNAMEEXISTS": "This username is already in use", "APP_SQUAWK_DELETECOMMENT": "Are you sure want to delete your comment?", "APP_SQUAWK_DELETEPOST": "Are you sure want to delete your post?", "APP_SQUAWK_VOTES": "Votes", "APP_SQUAWK_LEFTDAYVOTE": "days left", "APP_SQUAWK_LEFTHOURSVOTE": "hours left", "APP_SQUAWK_LEFTMINUTESVOTE": "minutes left", "APP_SQUAWK_LEFTSECONDSVOTE": "seconds left", "APP_SQUAWK_VOTESFINISH": "Final results", "APP_SQUAWK_DELETEDPOST": "Post deleted", "APP_SQUAWK_DELETEDCOMMENT": "Comment deleted", "APP_SQUAWK_LOGGEDPOST": "You must be logged in to post", "APP_SQUAWK_TEXTPOSTERROR": "You must enter a text to post", "APP_SQUAWK_POSTCHARACTERSERROR": "You can't post more than 500 characters", "APP_SQUAWK_POSTIMAGEERROR": "You can't post more than 4 image", "APP_SQUAWK_POSTSUCCESS": "Your post has been shared", "APP_SQUAWK_ALREADYVOTEDPOLL": "You have already voted in this poll", "APP_SQUAWK_VOTEDPOLLEND": "The poll is over", "APP_SQUAWK_FORYOU": "For You", "APP_SQUAWK_FOLLOWING": "Following", "APP_SQUAWK_LOGINACCOUNT": "Login to your account", "APP_SQUAWK_LOGIN": "<PERSON><PERSON>", "APP_SQUAWK_USERNAME": "Username*", "APP_SQUAWK_USERNAMEHINT": "Username must be greater than 4 characters", "APP_SQUAWK_PASSWORD": "Password*", "APP_SQUAWK_PASSWORDHINT": "Password must be greater than 6 characters", "APP_SQUAWK_DISPLAYNAME": "Display Name*", "APP_SQUAWK_DISPLAYNAMEHINT": "Display Name must be greater than 4 characters", "APP_SQUAWK_CONFIRMPASSWORD": "Confirm Password*", "APP_SQUAWK_CONFIRMPASSWORDHINT": "Confirm Password", "APP_SQUAWK_STEP": "Step", "APP_SQUAWK_NEXT": "Next", "APP_SQUAWK_LOGINERROR": "Username or password is incorrect", "APP_SQUAWK_NEWSQUAWK": "What is happening?", "APP_SQUAWK_NEWCHOICE": "New Choice", "APP_SQUAWK_POLLDATEEND": "Poll End Date:", "APP_SQUAWK_POLLDAY": "Day", "APP_SQUAWK_POLLHOUR": "Hour", "APP_SQUAWK_POLLMINUTE": "Minute", "APP_SQUAWK_POLLTWOLEAST": "You must have at least 2 choices.", "APP_SQUAWK_POSTBANNED": "You can't post because your account is banned", "APP_SQUAWK_POSTSUCESS": "Your post has been shared", "APP_SQUAWK_POSTERROR": "An error occurred while sharing your post", "APP_SQUAWK_URL": "URL", "APP_SQUAWK_NOTAUTH": "See what's", "APP_SQUAWK_NOTAUTH2": "happening in the", "APP_SQUAWK_NOTAUTH3": "world right now.", "APP_SQUAWK_SIGNUP": "Sign Up", "APP_SQUAWK_NOTIFLIKED": " liked your post", "APP_SQUAWK_NOTIFCOMMENTED": " commented on your post.", "APP_SQUAWK_NOTIFMENTIONED": " mentioned you in a post", "APP_SQUAWK_NOTIFFOLLOWED": " started following you", "APP_SQUAWK_NOTIFREPOST": " reposted your post", "APP_SQUAWK_NOTNOTIFI": "You don't have any notifications", "APP_SQUAWK_POSTS": "Posts", "APP_SQUAWK_LIKES": "<PERSON>s", "APP_SQUAWK_REPOSTS": "Reposts", "APP_SQUAWK_PINNEDPOST": "Pinned Post", "APP_SQUAWK_ACCOUNTSUSPENDED": "Account Suspended", "APP_SQUAWK_ACCOUNTSUSPENDEDEXP": "This account has been suspended. Learn more about why Squawk suspends accounts, or return to your timeline.", "APP_SQUAWK_UNPINNED": "Your post has been unpinned", "APP_SQUAWK_PINNED": "Your post is pinned", "APP_SQUAWK_FOLLOWERROR": "You can't follow yourself", "APP_SQUAWK_FOLLOWED": "You are now following", "APP_SQUAWK_UNFOLLOWED": "You are no longer following", "APP_SQUAWK_CREATEACC": "Create your account", "APP_SQUAWK_WITHURL": "with URL", "APP_SQUAWK_WITHCAMERA": "with Camera", "APP_SQUAWK_AVATARURL": "Avatar Image URL", "APP_SQUAWK_BANNERURL": "Banner Image URL", "APP_SQUAWK_USERNAMEERROR": "Username must be greater than 4 characters", "APP_SQUAWK_DISPLAYNAMEERROR": "Display Name must be greater than 4 characters", "APP_SQUAWK_PASSWORDERROR": "Password must be greater than 6 characters", "APP_SQUAWK_PASSWORDNOTMATCH": "Passwords don't match", "APP_SQUAWK_REGISTERFAILED": "Registration failed", "APP_SQUAWK_REGISTERED": "You have successfully registered", "APP_SQUAWK_SEARCH": "Search", "APP_SQUAWK_TRENDSFORYOU": "Trends for you", "APP_SQUAWK_NOTWHATSTRENDING": "There are currently no trends for you", "APP_SQUAWK_VERIFIED": "Verified", "APP_SQUAWK_CHANGEPASSWORD": "Change Password", "APP_SQUAWK_LOGOUT": "Logout", "APP_SQUAWK_AVATARCHANGED": "Avatar changed successfully", "APP_SQUAWK_BANNERCHANGED": "Banner changed successfully", "APP_SQUAWK_PASSWORDCHANGED": "Password changed successfully", "APP_SQUAWK_VERIFEDDESC": "Get the unique verified feature by purchasing Squawk verified!", "APP_SQUAWK_VERIFEDDESC2": "Only $15 / monthly", "APP_SQUAWK_VERIFEDSUCCESS": "You have successfully purchased the verified feature", "APP_SQUAWK_VERIFEDERROR": "You can't purchase the verified feature because you don't have enough money", "APP_SQUAWK_VERIFEDERROR2": "You can't purchase the verified feature because you already have it", "APP_SQUAWK_POSTAUDIOERROR": "Voice record must longer 1 second or may be problem upload service", "APP_SQUAWK_TYPEMEDIAERROR": "Only one type of media can be shared in a post", "APP_SQUAWK_NOTIFYERROR": "This post deleted", "APP_SQUAWK_EMPTYTWEETFOLLOWING": "You aren't following anyone.", "APP_SQUAWK_EMPTYTWEET": "There are no posts shared yet.", "APP_SQUAWK_NEWTITLE": "New Squawk", "APP_SQUAWK_NOTIFICATIONS": "Notifications", "APP_SQUAWK_TOTALPOST": "Posts", "APP_SQUAWK_TOTALFOLLOWERS": "Followers", "APP_SQUAWK_TOTALFOLLOWING": "Following", "APP_SQUAWK_IMAGESIZECHECK": "Image size checking..", "APP_SQUAWK_USERNAMELATIN": "You can't use special characters (Only use A-Z, 0-9)", "APP_SQUAWK_NOTIFY_LIKE": "{name} liked your post", "APP_SQUAWK_NOTIFY_RETWEET": "{name}  eposted your post", "APP_SQUAWK_NOTIFY_COMMENT": "{name}  commented on your post", "APP_SQUAWK_NOTIFY_FOLLOW": "{name}  started following you", "APP_SQUAWK_NOTIFY_MENTIONING": "{name}  mentioned you in a post", "APP_SQUAWK_NOTFIY_POST": "{name}  shared a new post", "APP_SQUAWK_POSTING_PRELOADER": "Posting...", "APP_SQUAWK_BANNERERROR_LENGTH": "Banner URL must be between 6 and 255 characters", "APP_SQUAWK_BANNER": "Banner", "APP_SQUAWK_AVATAR": "Avatar"}, "BankAPP": {"APP_BANK_TITLE": "Bank", "APP_BANK_HISTORY": "History", "APP_BANK_INCOME": "Income", "APP_BANK_EXPENSE": "Expense", "APP_BANK_NOHISTORY": "No history found", "APP_BANK_TRANSFER": "Transfer", "APP_BANK_QUICKSEND": "Quick Send", "APP_BANK_RECENTSHISTORY": "Recent History", "APP_BANK_SEEALL": "See All", "APP_BANK_QUICKSENDAMOUNT": "How much money do you want to send to {name}?", "APP_BANK_QUICKSENDAMOUNTERROR": "Balance cannot be zero or negative.", "APP_BANK_QUICKSENDAMOUNTERROR2": "You do not have enough balance. Your balance is {amount}.", "APP_BANK_QUICKSENDAMOUNTERROR3": "You cannot send money to yourself.", "APP_BANK_QUICKSENDAMOUNTERROR4": "Please select a contact or enter a phone number to send money.", "APP_BANK_QUICKSENDCONFIRM": "Are you sure you want to send {amount} to {name} ? The fee for this transaction is {fee}.", "APP_BANK_SENDING": "Processing...", "APP_BANK_QUICKSENDSUCCESS": "Money sent successfully", "APP_BANK_TOTALBALANCE": "Total Balance", "APP_BANK_TRANSFERDESCRIPTION_FEE": "Transfer fee", "APP_BANK_TRANSFERNOTIFICATION": "{name} send you {money}", "APP_BANK_TRANSFERDESCRIPTION_SENDER": "You have sent money to {name}.", "APP_BANK_TRANSFERDESCRIPTION_RECEIVER": "You have received money from {name}.", "APP_BANK_SENDMONEY_ERROR": "An error occurred while sending money", "APP_BANK_SENDMONEY_ERROR_INVALID": "Please enter a valid amount or select a contact", "APP_BANK_SENDMONEY_ERROR_INVALID_PHONE": "There was a problem with your phone", "APP_BANK_SENDMONEY_ERROR_NOT_OWNER": "This transfer failed because you do not own the phone.", "Billing": {"APP_BILLING_TITLE": "Billing", "APP_BILLING_AMOUNT_PAYABLE": "Amount Payable", "APP_BILLING_AMOUNT": "Billing Amount", "APP_BILLING_DELETE_SUCCESS": "Billing deleted successfully", "APP_BILLING_DELETE_FAIL": "Failed to delete billing", "APP_BILLING_DELETE_PROCESSING": "Deleting billing...", "APP_BILLING_DELETE_CONFIRM": "Are you sure you want to delete billing #{billingid} ? This action cannot be undone.", "APP_BILLING_JOB": "Business", "APP_BILLING_UNPAID": "Unpaid", "APP_BILLING_PAID": "Paid", "APP_BILLING_PAY_ALL": "Pay All", "APP_BILLING_PAY": "Pay", "APP_BILLING_NEW_BILLING": "Create", "APP_BILLING_INVALID_AMOUNT": "Please enter a valid amount", "APP_BILLING_INVALID_DESCRIPTION": "Please enter a fine desc", "APP_BILLING_INVALID_PLAYERID": "Please select a player or enter Player ID", "APP_BILLING_CREATING_BILLING": "Sending billing...", "APP_BILLING_PAY_CONFIRM": "Are you sure you want to pay {amount} for billing #{billingid}?", "APP_BILLING_PAY_PROCESSING": "Processing payment...", "APP_BILLING_PAY_SUCCESS": "Billing payment successful", "APP_BILLING_PAY_FAIL": "Failed to pay billing", "APP_BILLING_NO_BILLINGS": "No billings to pay", "APP_BILLING_PAY_ALL_CONFIRM": "Are you sure you want to pay all billings? Total amount: {amount}", "APP_BILLING_NOTIFY_NEWBILL": "You have a new bill", "APP_BILLING_PLAYERFEE": "The billing you issued has been paid", "APP_BILLING_ALLPAIDDESCRIPTION": "You have paid all invoices amount paid : {amount}", "APP_BILLING_PAIDDESCRIPTION": "You have paid the invoice amount paid : {amount}", "APP_BILLING_PLAYER_NOT_ONLINE": "{blplayer} player offline", "APP_BILLING_SUCCESS": "The invoice was issued successfully", "APP_BILLING_ERROR": "An error occurred while issuing the invoice", "APP_BILLING_AMOUNT_ERROR": "Amount <PERSON>r", "APP_BILLING_YOURSELF_ERROR": "You cannot invoice yourself", "APP_BILLING_NOBILL": "You don't have any bills.", "APP_BILLING_NO_FOUND": "No bills found", "Modal": {"CONTACTS": "Contacts", "PLAYERID": "PlayerID", "AIRSHARE": "AirShare", "BUSINESS": "Business", "PHONE": "Phone", "CHOOSE_PERSON": "Choose <PERSON>", "INPUT_PLAYERID": "Enter Player ID", "INPUT_PHONE_NUMBER": "Enter Phone Number", "INPUT_BILLING_DESCRIPTION": "Enter Billing Description", "SEND": "Send"}}}, "MailAPP": {"APP_MAIL_INBOX": "Inbox", "APP_MAIL_EMPTYMAIL": "The mailbox is empty.", "APP_MAIL_SEARCHMAIL": "Search Mail", "APP_MAIL_MODAL_DELETETITLE": "Delete Mail", "APP_MAIL_MODAL_DELETEDESC": "Are you sure you want to delete this mail?", "APP_MAIL_MODAL_DEL": "Delete", "APP_MAIL_MODAL_CANCEL": "Cancel", "APP_MAIL_NOTIFY": "{sender} sent you a new e-mail", "APP_MAIL_TITLE": "Mail", "APP_MAIL_DELETEALLDESC": "Are you sure you want to delete all e-mails?", "APP_MAIL_DELETEALLTITLE": "Delete All", "APP_MAIL_DELETEALLERROR": "An error occurred while deleting all e-mails.", "APP_MAIL_DELETEALLSUCCESS": "All e-mails have been deleted.", "APP_MAIL_DELETEALLLOADING": "Deleting all e-mails..."}, "DarkChatAPP": {"APP_DARKTCHAT_TITLE": "Dark Chat", "APP_DARKTCHAT_NEW_CHANNEL": "Add Channel", "APP_DARKTCHAT_CHANNELNAME": "Set the channel name", "APP_DARKTCHAT_CHANNELNAME_MIN": "Numbers can't be used in the channel name and must be longer than 5 characters", "APP_DARKTCHAT_REMOVE_CHANNELTITLE": "Remove Channel", "APP_DARKTCHAT_REMOVE_CHANNELDESC": "Are you sure you want to remove this channel?", "APP_DARKTCHAT_SET_GPS": "Set GPS", "APP_DARKTCHAT_REMOVE_CHANNELBUTTON": "Remove", "APP_DARKCHAT_NOTIFY_NEWMESSAGE": "There is a new message on {channel}"}, "AdvertisingAPP": {"APP_AD_TITLE": "Advertising", "APP_AD_HEADER": "Feed", "APP_AD_FAV": "Favorites", "APP_AD_PROFILE": "Profile", "APP_AD_MODAL_NEWADD": "New Ad", "APP_AD_SHAREAD": "Share Ad", "APP_AD_SEARCH": "Search", "APP_AD_MODAL_CANCEL": "Cancel", "APP_AD_MODAL_SHARE": "Share", "APP_AD_MODAL_FILTERSELECT": "Select Filter", "APP_AD_MODAL_IMAGEADD": "Add Image", "APP_AD_MODAL_TEXTAREALABEL": "Write a message", "APP_AD_MODAL_TEXTAREAPLACEHOLDER": "Write a message", "APP_AD_SHAREERRORCATEGORY": "You must select a category", "APP_AD_IMAGELINK": "Image Link", "APP_AD_IMAGELINKDESC": "Add Image Link", "APP_AD_IMAGE_NOTFOUND": "Image not found", "APP_AD_SHAREMINCHARACTER": "You must enter at least 15 characters", "APP_AD_SHARECONFIRM_DESC": "Are you sure you want to create an ad? Transaction fee: {fee}", "APP_AD_SHARE_ERROR_NOMONEY": "You don't have enough money", "APP_AD_SHARE_NOTFY_POST": "{name} shared a new advertisement"}, "NewsAPP": {"APP_NEWS_HEADER": "News", "APP_NEWS_HOME": "Home", "APP_NEWS_ADDNEWS": "Add News", "APP_NEWS_LISTNEWS": "News", "APP_NEWS_TITLE": "Title", "APP_NEWS_VIDEOID": "Video ID", "APP_NEWS_URLPICTURE": "Picture URL (.png, .jpg)", "APP_NEWS_DETAIL": "Detail", "APP_NEWS_SAVE": "Share", "APP_NEWS_NEWSSEARCH": "Search", "APP_NEWS_NOTIFY_NEW": "A news has been added", "APP_NEWS_DETAILTITLE": "News Text"}, "ValeAPP": {"APP_VALE_TITLE": "Vale", "APP_VALE_CARTYPE": "Car Type", "APP_VALE_CARMAXSPEED": "Max Speed", "APP_VALE_KMH": "km/h", "APP_VALE_GARAGE": "Garage", "APP_VALE_BRING": "BRING", "APP_VALE_TRACK": "TRACK", "APP_VALE_SEARCH": "Search", "APP_VALE_MODAL_BRING": "Would you like the valet to bring your car?", "APP_VALE_ERROR_BRING_NOMONEY": "You don't have enough money", "APP_VALE_ERROR_BRING_CARNOTFOUND": "Your car wasn't found", "APP_VALE_ERROR_BRING_CARIMPOUNDED": "Your vehicle can't be summoned because it has been seized.", "APP_VALE_ERROR_BRING_NOTINGARAGE": "Your car isn't in the garage", "APP_VALE_ERROR_BRING_CAROUTSIDE": "The car can't be brought because it is outside.", "APP_VALE_SUCESS_BRING": "Vehicle on the road", "APP_VALE_MARKED": "Your vehicle marked on the map", "APP_VALE_ERROR_TRACK_CARGARAGE": "Your vehicle isn't marked because it is in the garage.", "APP_VALE_ERROR_CARBLACKLIST": "This vehicle model cannot be brought", "APP_VALE_USEVALET_BANK_HISTORY_DESC": "Use of valet service"}, "GalleryAPP": {"APP_GALLERY_TITLE": "Gallery", "APP_GALLERY_ALL_DELETEDESC": "Are you sure you want to delete all photos?", "APP_GALLERY_ALL_DELETE_TITLE": "Delete All", "APP_GALLERY_LOADING": "Loading...", "APP_GALLERY_OPTION": "Options", "APP_GALLERY_OPTION_DELETE": "Delete", "APP_GALLERY_OPTION_FAVORITES": "Favorites", "APP_GALLERY_OPTION_FAV_LOADING": "Adding to favorites...", "APP_GALLERY_OPTION_SEND": "Send", "APP_GALLERY_OPTION_CANCEL": "Cancel", "APP_GALLERY_ADDIMAGE": "Add Image", "APP_GALLERY_ADDIMAGEDESC": "Picture link", "APP_GALLERY_IMAGE_TYPE": "Make sure the link is an image", "APP_GALLERY_IMAGE_TYPE_TITLE": "Image type error", "APP_GALLERY_IMAGE_SIZE_LIMIT": "Image size limit is {size} MB", "APP_GALLERY_IMAGE_SIZE_LIMIT_TITLE": "Image size", "APP_GALLERY_ERROR_TITLE": "Error", "APP_GALLERY_ERROR_DESC": "An error occurred while adding the image", "APP_GALLERY_ZOOM": "Zoom", "APP_GALLERY_OPTION_FAVORITE_ADD": "Add Favorite", "APP_GALLERY_OPTION_FAVORITE_REMOVE": "Remove Favorite"}, "GpsAPP": {"APP_GPS_TITLE": "GPS", "APP_GPS_NAMEWRITE": "Set GPS Name", "APP_GPS_OPTION_DELETE": "Delete", "APP_GPS_OPTION_SEND": "Send", "APP_GPS_OPTION_CANCEL": "Cancel", "APP_GPS_MESSAGEERROR": "An error occurred while sending the message.", "APP_GPS_LOCATIONMARK": "Mark the location", "APP_GPS_MARKETLOCATION": "Location marked", "APP_LIVE_ALDREADYMARKED": "Location already marked", "APP_LIVE_LOCATIONEXPIRED": "Live location expired", "APP_LIVE_NOTFOUNDSHARING": "Live location sharing destination not found", "APP_LIVE_MARKEDONMAP": "Live location marked on map", "APP_GPS_MYLOCATION": "My Location", "APP_GPS_LOCATIONERROR": "An error occurred while getting your location", "APP_GPS_OPTIONS": "Options"}, "StockMarket": {"APP_STOCKMARKET_TITLE": "Stock Market", "APP_STOCKMARKET_HELLO": "Hello", "APP_STOCKMARKET_TOTALBALANCE": "Total Balance", "APP_STOCKMARKET_NOTCRYPTO": "No Crypto Found in Your Account", "APP_STOCKMARKET_ADDCOIN": "Add Coin", "APP_STOCKMARKET_TRANSFER": "Transfer", "APP_STOCKMARKET_HISTORY": "History", "APP_STOCKMARKET_WALLET": "Wallet", "APP_STOCKMARKET_SEND": "Send", "APP_STOCKMARKET_TRANSACTIONS": "Recent Transactions", "APP_STOCKMARKET_SELL": "<PERSON>ll", "APP_STOCKMARKET_BUY": "Buy", "APP_STOCKMARKET_SELLBUTTON": "Sell {coin}", "APP_STOCKMARKET_BUYBUTTON": "Buy {coin}", "APP_STOCKMARKET_SEARCH": "Search", "APP_STOCKMARKET_BUYHEADER": "Buy {coin}", "APP_STOCKMARKET_BUYDESC": "How much {coin} do you want to buy?", "APP_STOCKMARKET_SELLHEADER": "Sell {coin}", "APP_STOCKMARKET_SELLDESC": "How much {coin} do you want to sell?", "APP_STOCKMARKET_NOTIFY_TRANSFERSUCESS": "{amount} {coin} transfers were made to you", "APP_STOCKMARKET_NOTIFY_BUYSUCESS": "You purchased {amount} coin", "APP_STOCKMARKET_NOTIFY_SELLSUCESS": "You sold {amount} coin", "APP_STOCKMARKET_MODAL_CANCEL": "Cancel", "APP_STOCKMARKET_MODAL_SELECTCOIN": "Select Coin", "APP_STOCKMARKET_MODAL_SELECT": "Select", "APP_STOCKMARKET_NOTIFY_TRANSFERSUCESSTWO": "{amount} coin transferred", "APP_STOCKMARKET_LOADING": "Loading...", "APP_STOCKMARKET_TRANSFER_SUCCESS": "Transfer successful", "APP_STOCKMARKET_TRANSFER_ERROR": "An error occurred while transferring", "APP_STOCKMARKET_BUYLOADING": "Buying...", "APP_STOCKMARKET_BUYNOTIFYDESC": "You have successfully purchased {amount} {coin}", "APP_STOCKMARKET_SELLLOADING": "Selling...", "APP_STOCKMARKET_SELLNOTIFYDESC": "You have successfully sold {amount} {coin}", "APP_STOCKMARTKET_NOMONEY": "You don't have enough money"}, "Tuneify": {"APP_TUNEIFY_TITLE": "Tuneify", "APP_TUNEIFY_WALLPAPER": "Wallpaper", "APP_TUNEIFY_RINGTONES": "Ringtones", "APP_TUNEIFY_NORESULT": "No results found", "APP_TUNEIFY_SEARCH_CANCEL": "Cancel", "APP_TUNEIFY_SEARCH_PLACEHOLDER": "Search", "APP_TUNEIFY_SETRINGTONE": "Set as ringtone", "APP_TUNEIFY_SETNOTIFICATION": "Set as notification", "APP_TUNEIFY_SEARCH_BLOCKED_WORD": "The search term contains banned words"}, "GigglesAPP": {"APP_GIGGLES_TITLE": "<PERSON><PERSON><PERSON>", "APP_GIGGLES_SEARCH": "Search"}, "NoteAPP": {"APP_NOTE_HEADER": "Note", "APP_NOTE_TITLE": "Title", "APP_NOTE_NOTE": "Note", "APP_NOTE_ADDED": "Note added", "APP_NOTE_DELETED": "Note deleted", "APP_NOTE_DONTNOTE": "You don't have any notes.", "APP_NOTE_NEW": "New Note", "APP_NOTE_ADDIMAGE": "Add image (optional)", "APP_NOTE_ADDIMAGEMODAL": "Add Image", "APP_NOTE_ADDIMAGESAVEMODALBUTTON": "Save", "APP_NOTE_EDITNOTE": "Edit Note", "APP_NOTE_DELETENOTEMODAL": "Are you sure want to delete this note?", "APP_NOTE_DELETENOTE": "Delete Note", "APP_NOTE_MODAL_DELETEBUTTON": "Delete Note", "APP_NOTE_DELETEIMAGEMODAL": "Are you sure you want to delete this image?", "APP_NOTE_SAVENOTEMODAL": "Are you sure you want to save this note?", "APP_NOTE_SAVENOTE": "Save Note", "APP_NOTE_SHARED": "Your note has been shared", "APP_NOTE_PNOTFOUND": "{ntplayer} player not found", "APP_NOTE_UPDATED": "Note updated", "APP_NOTE_SHARENOTE": "Note shared, do you want to accept it?"}, "HouseAPP": {"APP_HOUSE_ME": "Me", "APP_HOUSE_TITLE": "House", "APP_HOUSE_KEYS": "Keys", "APP_HOUSE_TIER": "Tier", "APP_HOUSE_TRANSFER": "Transfer", "APP_HOUSE_CANCEL": "Cancel", "APP_HOUSE_SETGPS": "Location marked", "APP_HOUSE_TRANSFER_ERROR": "An error occurred while transferring the house", "APP_HOME_TRANSFERSUCCESS": "House transferred successfully"}, "QbitAPP": {"APP_QBIT_TITLE": "Qbit", "APP_QBIT_VALUEC": "Value Change", "APP_QBIT_CURRENCY": "USD", "APP_QBIT_BUYHERE": "You can buy crypto here.", "APP_QBIT_SELLHERE": "You can sell crypto here.", "APP_QBIT_TRANSFERTEXT": "You can transfer crypto here.", "APP_QBIT_BANKTOTALBALANCE": "Total Balance", "APP_QBIT_PAY": "Pay", "APP_QBIT_SELL": "<PERSON>ll", "APP_QBIT_TRANSFER": "Transfer", "APP_QBIT_PHONENUMBER": "Phone Number", "APP_QBIT_NOTIFY_TRANSFERSUCESS": "You transferred successfully", "APP_QBIT_NOTIFY_BUYSUCESS": "You have successfully purchased", "APP_QBIT_NOTIFY_SELLSUCESS": "You sold successfully", "APP_QBIT_NOPERSON": "Person not found"}, "RentACarAPP": {"APP_RENTACAR_HELLO": "Hello, {name}", "APP_RENTACAR_TITLE": "Rent A Car", "APP_RENTACAR_SLOGAN": "Pay as you use!", "APP_RENTACAR_DK": "mins", "APP_RENTACAR_PHOTO": "Car Check", "APP_RENTACAR_OPENDOORCAR": "Open the vehicle doors", "APP_RENTACAR_CARDELIVERY": "Delivery Point", "APP_RENTACAR_MOOV": "Rent A Car", "APP_RENTACAR_VEHICLEDELIVER": "Deliver Vehicle", "APP_RENTACAR_NOSTOCK": "No Stock", "APP_RENTACAR_LOADING": "Queued, please wait for notification", "APP_RENTACAR_CURRENCY": "$", "APP_RENTACAR_RENTCAR": "Pick up vehicle from marked location on map", "APP_RENTACAR_RENTCARERROR": "You don't have enough money", "APP_RENTACAR_TOOFAR": "You are too far from vehicle", "APP_RENTACAR_NOVEHICLE": "Rental vehicle not found", "APP_RENTACAR_DELIVERY": "Delivery point marked on the map", "APP_RENTACAR_DELIVERYOUTSIDE": "You can't end vehicle rental outside delivery point", "APP_RENTACAR_RENTENDERROR": "You can't end a rental now", "APP_RENTACAR_ENGINEBROKE": "The vehicle engine broke, deliver it immediately", "APP_RENTACAR_DELIVERINGCAR": "Being delivered, please wait", "APP_RENTACAR_CARCHECK": "Vehicle checked", "APP_RENTACAR_ALREADYRENTED": "The vehicle is already rented", "APP_RENTACAR_NOCARFOUND": "Car not found"}, "CarSellerAPP": {"APP_CARSELLER_SEARCH": "Search", "APP_CARSELLER_TITLE": "<PERSON>", "APP_CARSELLER_MAXSPEED": "Max Speed", "APP_CARSELLER_POST": "Post", "APP_CARSELLER_SELLVEHICLE": "This car is in the ad!", "APP_CARSELLER_DELETE": "Delete", "APP_CARSELLER_DELETING": "Deleting...", "APP_CARSELLER_DELERROR": "An error occurred while deleting the car from the ad.", "APP_CARSELLER_DELETEVEHICLE": "Are you sure you want to remove the car from the ad?", "APP_CARSELLER_KMH": "km/h", "APP_CARSELLER_PLATE": "Plate", "APP_CARSELLER_MODAL_SELECTPHOTO": "Select Photo", "APP_CARSELLER_CARSELLINGPRICE": "Determine the selling price of the car", "APP_CARSELLER_CONTINUE": "Continue", "APP_CARSELLER_CARSELLINGAPPROVAL": "Are you sure you want to add the car to the ad? <br> Application usage fee is {fee}", "APP_CARSELLER_SELLING": "Selling...", "APP_CARSELLER_SELERROR": "An error occurred while adding the car to the ad.", "APP_CARSELLER_PUBDATE": "Release Date", "APP_CARSELLER_MODAL_BUYTITLE": "Buy Car", "APP_CARSELLER_MODAL_BUYDESC": "Are you sure you want to buy this car? <br> Transaction fee: {fee}", "APP_CARSELLER_MODAL_BUYBUTTON": "Buy", "APP_CARSELLER_MODAL_BUYLOADING": "Buying...", "APP_CARSELLER_MODAL_BUYSUCCESS": "You have successfully purchased the car.", "APP_CARSELLER_MODAL_BUYERROR": "An error occurred while purchasing the car.", "APP_CARSELLER_BANKBUY": "Vehicle Purchase", "APP_CARSELLER_BANKSELLER": "Vehicle Sales", "APP_CARSELLER_SOLDNOTIFY": "Your vehicle with plate number {plate} has been sold", "APP_CARSELLER_VEHICLENEARBYERROR": "You must be near your vehicle to create a vehicle ad", "APP_CARSELLER_BLACKLISTEDVEHICLE": "This vehicle model cannot be sold"}, "MDTAPP": {"APP_MDT_APPTITLE": "MDT", "APP_MDT_WANTED": "Wanted", "APP_MDT_REASON": "Reason", "APP_MDT_APPEARANCE": "Appearance", "APP_MDT_LASTSEEN": "Last Seen", "APP_MDT_LICENSEPLATE": "License Plate", "APP_MDT_PERSON": "Person", "APP_MDT_VEHICLE": "Vehicle", "APP_MDT_OWNER": "Owner", "APP_MDT_MOT": "Mo<PERSON>", "APP_MDT_SIGNALED": "Signaled", "APP_MDT_ADRESS": "<PERSON>ress", "APP_MDT_BIRTHDAY": "Birth Date", "APP_MDT_NATIONALTY": "Nationalty", "APP_MDT_GENDER": "Gender", "APP_MDT_APARTMENT": "Aparment", "APP_MDT_DRIVERLINCESE": "Drivers License", "APP_MDT_ADDWANTED": "Add Wanted", "APP_MDT_HOUSETITLE": "House", "APP_MDT_HOUSE_TIER": "Tier", "APP_MDT_GARAGE": "Garage", "APP_MDT_YES": "Yes", "APP_MDT_NO": "No", "APP_MDT_SEARCH": "Search", "APP_MDT_FEMALE": "Female", "APP_MDT_MALE": "Male", "APP_MDT_PHONENUMBER": "Phone Number", "APP_MDT_SEARCHPERSONWAIT": "Searching person please wait...", "APP_MDT_TIME": "Time", "APP_MDT_ALREADYWANTED": "There is already a wanted log for this person", "APP_MDT_PLATEINVALID": "This plate is invalid"}, "AppStoreAPP": {"APP_APPSTORE_TITLE": "App Gallery", "APP_APPSTORE_UNINSTALL": "Remove", "APP_APPSTORE_INSTALL": "Get"}, "GamesAPP": {"APP_GAME_TITLE": "Games", "APP_GAME_SCORE": "Score", "APP_GAME_DHAND": "Dealer Hand: ", "APP_GAME_YHAND": "Your Hand: ", "APP_GAME_CREDIT": "Credit: ", "APP_GAME_BET": "Bet: ", "APP_GAME_HIT": "Hit", "APP_GAME_STAND": "Stand", "APP_GAME_DOUBLE": "DOUBLE", "APP_GAME_RESUME": "Resume", "APP_GAME_STARTGAME": "Start Game", "APP_GAME_CURRENCY": "$", "APP_GAME_BLACKJACKDRAW": "Draw", "APP_GAME_BLACKJACKLOSE": "You Lose", "APP_GAME_BLACKJACKWIN": "You Win", "APP_GAME_SNAKE_GAMEOVER": "Game Over!", "APP_GAME_RESTART": "<PERSON><PERSON>", "APP_GAME_2048_KEYBOARD": "Keyboard : ↑ ↓ ← →", "APP_GAME_BEST_SCORE": "Best", "APP_GAME_BEST_RESTART_INFO": "Restart with the reset button", "APP_GAME_2048_MOVECOUNT": "Move: ", "APP_GAME_WIN": "You Win!", "APP_GAME_OVER": "Game Over", "APP_GAME_FINAL_SCORE": "Final Score: ", "APP_GAME_PLAY_AGAIN": "Play Again", "APP_GAME_SNAKE_GAME": "Snake Game", "APP_GAME_LEVEL": "Level", "APP_GAME_SPEED": "Speed", "APP_GAME_READY_TO_PLAY": "Ready to Play?", "APP_GAME_SNAKE_CONTROLS": "Use arrow keys or touch controls", "APP_GAME_SNAKE_PLAY": "Play Game", "APP_GAME_SNAKE_LEVEL_REACHED": "Level Reached: ", "APP_GAME_NEW_RECORD": "🎉 New Record!", "APP_GAME_TETRIS_GAME": "Tetris Game", "APP_GAME_TETRIS_LINES": "Lines", "APP_GAME_TETRIS_LEVELUP": "Clear 10 lines to level up!", "APP_GAME_TETRIS_LINESCLEARED": "Lines Cleared: ", "APP_GAME_TETRIS_SPEED_MASTER": "🏆 Speed Master!", "APP_GAME_TETRIS_LINE_CLEARER": "🔥 Line Clearer!", "APP_GAME_TETRIS_NEXT_PIECE": "Next Piece"}, "LiveStreamAPP": {"APP_LIVE_TITLE": "Live Stream", "APP_LIVE_CREATE": "Create", "APP_LIVE_MODAL_TITLE": "Create Live Stream", "APP_LOVE_MODAL_STREAMTITLE": "Live Stream Title", "APP_LIVE_MODAL_BUTTON": "Create", "APP_LIVE_LIVES": "Streams", "APP_LIVE_NOLIVE": "There are currently no live streams available. Please check later.", "APP_LIVE_STOPSTREAM": "Stop Stream", "APP_LIVE_LIVE": "Live", "APP_LIVE_MESSAGEHOLDER": "Type your comment...", "APP_LIVE_DONATE": "Donate", "APP_LIVE_BLOCKEDMESSAGE": "User Blocked, Messages Deleted by Streamer.", "APP_LIVEAPP_TURNOFFERROR": "You need to wait a while to turn off the broadcast, try this process again later", "APP_LIVE_SENDBLOCKEDMESSAGE": "You can't send messages. You have been blocked by the streamer.", "APP_LIVE_SENDBLOCKEDDONATE": "You can't send donate. You have been blocked by the streamer.", "APP_LIVE_SENDSPAM": "You can't send messages this fast. Please wait and try again.", "APP_LIVE_SENDSPAM2": "You must wait 20 seconds to send a new message.", "APP_LIVE_CONNETLOADING": "Connecting...", "APP_LIVE_MODAL_STOPSTREAMTITLE": "Stop Stream", "APP_LOVE_MODAL_STOPSTREAMDESC": "Are you sure you want to stop the stream?", "APP_LIVE_MODAL_STOPSTREAMBUTTON": "Stop Stream", "APP_LIVE_MODAL_DONATE_TITLE": "Donate", "APP_LIVE_WALLET": {"Wallet": "Wallet", "Cheer": "Cheer", "Coin": "Coin", "GIF": "GIF", "Tax": "Tax: {tax}%", "YOU_BALANCECOIN": "Your Coin Balance: {balance}", "YOU_BALANCECHEER": "Your Cheer Balance: {balance}", "DONATION_AMOUNT": "Donation Amount", "TRANSFERAMOUNTTEXT": "Transfer Amount:", "AMOUNT": "Amount:", "TOTAL_TEXT": "Total =", "MINIMUMTRANSFER": "Minimum Transfer:", "BUY": "Buy", "TRANSFER_COIN": "Transfer Coin", "SELL": "<PERSON>ll", "DONATE": "Donate", "SEND_GIF": "Send GIF", "SEARCH_GIF": "Search GIF", "WALLET_ERROR": "An error occurred during the wallet transaction", "WALLET_SUCCESS": "Transaction successful", "WALLET_NOTENOUGHMONEY": "Not enough money", "WALLET_NOTENOUGHCOIN": "Not enough coins", "WALLET_NOTENOUGHCHEER": "Not enough cheers", "WALLET_MINIMUMTRANSFER_ERROR": "The minimum transfer amount must be {amount}", "CHEER_BANK_BUYDESC": "Cheer purchased.", "COIN_BANK_BUYDESC": "LiveStream earnings"}}, "SIMAPP": {"APP_SIM_TOTALPRICE": "Balance", "APP_SIM_MYACTIVITY": "My Activity", "APP_SIM_DATA": "Data", "APP_SIM_DATADETAIL": "{mb} MB", "APP_SIM_CALLS": "Minutes", "APP_SIM_CALLSDETAIL": "{min} min", "APP_SIM_SMS": "SMS", "APP_SIM_SMSDETAIL": "{sms} piece", "APP_SIM_PACKAGES": "{mobileservices} Packages", "APP_SIM_SELECTMOBILE": "Operators", "APP_SIM_NOTBUYPACKAGE": "You can't buy a new package because you have a package that isn't over yet.", "APP_SIM_NEWPACKAGE": "Are you sure you want to buy this package?", "APP_SIM_CONFIRM": "Confirm", "APP_SIM_LOADING": "Loading...", "APP_SIM_SUCESSPACKAGE": "You have successfully purchased the package.", "APP_SIM_ERRORPACKAGE": "An error occurred while purchasing a new package.", "APP_SIM_ERRORNUMBER": "An error occurred while purchasing a new number.", "APP_SIM_SELECTOPERATOR": "Select Operator", "APP_SIM_OPERATORSELECT": "Please select the operator you want to buy", "APP_SIM_NEWNUMBER": "New Number", "APP_SIM_NEWNUMBERBUY": "Are you sure you want to buy a new number?", "APP_SIM_CANCEL": "Cancel", "APP_SIM_BUY": "Buy", "APP_SIM_NEWNUMBERSUCESS": "Sucess, Your new number is {number}. Don't forget to buy package.", "APP_SIM_SUCESS": "Success", "APP_SIM_ERROR": "Error", "APP_SIM_BANKDETAIL": "Phone {value} usage", "APP_SIM_LIMITPHONENUMBER": "You can't buy a new number because you have reached the limit", "APP_SIM_NOTENOUGHMONEY": "You don't have enough money"}, "PlayTubeAPP": {"APP_PLAYTUBE_TITLE": "PlayTube", "APP_PLAYTUBE_SEARCH_CANCEL": "Cancel", "APP_PLAYTUBE_SEARCHPLACEHOLDER": "Search in playtube", "APP_PLAYTUBE_SEARCHLOADING": "Searching in playtube...", "APP_PLAYTUBE_NOURL": "Don't use search url", "APP_PLAYTUBE_NOSEARCH": "Search with keyword to watch video"}, "TaxiAPP": {"APP_TAXI_TITLE": "Taxi", "APP_TAXI_CUSTOMER_MAINTEXT": "Where are you going today?", "APP_TAXI_CUSTOMER_LOCATION": "Your Location", "APP_TAXI_CUSTOMER_DESTINATION": "Destination", "APP_TAXI_CUSTOMER_DESTINATIONERROR": "To be able to call a taxi, you must select the destination on map.", "APP_TAXI_CUSTOMER_DESTINATIONERROR2": "You must select the destination on map.", "APP_TAXI_CUSTOMER_TOTALDISTANCE": "Total Distance", "APP_TAXI_CUSTOMER_TOTALPRICE": "Total Price", "APP_TAXI_CUSTOMER_CALCULATE": "Calculate My Jouney", "APP_TAXI_CUSTOMER_CALLATAXI": "Call a Taxi", "APP_TAXI_CUSTOMER_STARTTAXI": "Your journey has begun", "APP_TAXI_CUSTOMER_START": "Start", "APP_TAXI_CUSTOMER_TOTAL": "Total", "APP_TAXI_CUSTOMER_STATUS": "Status", "APP_TAXI_CUSTOMER_TAXIEND": "End", "APP_TAXI_CUSTOMER_CANCEL": "Cancel", "APP_TAXI_CUSTOMER_TIPTITLE": "Tip Driver", "APP_TAXI_CUSTOMER_TIPDESC": "How much would you like to tip?", "APP_TAXI_CUSTOMER_LOADINGTIP": "Sending tip...", "APP_TAXI_CUSTOMER_TIPALERT": "You was tip the driver {tip}", "APP_TAXI_CUSTOMER_NOTTIPALER": "You can't tip the driver because you don't have enough money", "APP_TAXI_CUSTOMER_SEARCHING": "Searching for available taxi", "APP_TAXI_CUSTOMER_WAITING": "Taxi driver on the road", "APP_TAXI_CUSTOMER_CONTACTED": "Taxi driver picked you up from your location", "APP_TAXI_CUSTOMER_ENDED": "Taxi driver dropped you at your desired location", "APP_TAXI_DRIVER_CHOOSEJOB": "Choose job", "APP_TAXI_DRIVER_DESTINATION": "Destination", "APP_TAXI_DRIVER_TOTAL": "Total", "APP_TAXI_DRIVER_CUSTOMERLEFT": "The customer was left at the desired location", "APP_TAXI_DRIVER_CUSTOMERACCEPTED": "This customer accepted another taxi employee", "APP_TAXI_DRIVER_CUSTOMERWAITING": "Customer left to location", "APP_TAXI_DRIVER_CUSTOMERRECEIVED": "Customer Received", "APP_TAXI_DRIVER_ACCEPTJOB": "Accept job", "APP_TAXI_DRIVER_CANCELJOB": "Cancel", "APP_TAXI_DRIVER_LOADING": "Loading...", "APP_TAXI_DRIVER_ACCEPTJOBSTARTED": "You have accepted the job", "APP_TAXI_DRIVER_NOTJOBSTARTED": "You haven't accepted the job", "APP_TAXI_DRIVER_NOTFYCUSTOMERRECEIVED": "Customer received", "APP_TAXI_DRIVER_NOTFYNOTCUSTOMERRECEIVED": "Customer not received", "APP_TAXI_DRIVER_CUSTOMERLEFTLOCATION": "Customer left to location", "APP_TAXI_DRIVER_NOTCUSTOMERLEFTLOCATION": "Customer not left to location", "APP_TAXI_DRIVER_NOTIFY_NEWJOB": "You got a new job call", "APP_TAXI_CUSTOMER_NOTIFY_DRIVERACCEPTJOB": "A taxi driver accepted the job", "APP_TAXI_DRIVER_NOTCUSTOMER": "There are currently no jobs available", "APP_TAXI_CUSTOMER_OPTIONS_AUTHORIZED": "Become a taxi driver", "APP_TAXI_CUSTOMER_OPTIONS_UNAUTHORIZED": "Quit your job", "APP_TAXI_AI_GO": "Driverless taxi on the way", "APP_TAXI_BILLING_DESC": "Taxi service usage", "APP_TAXI_AI_PLAYER_START_NOTIFY": "Get in a taxi and click start from the application", "APP_TAXI_AI_START": "Start", "APP_TAXI_CUSTOMER_AITAXIERROR": "Taxi is not ready, please wait", "APP_TAXI_AI_NAME": "Driverless taxi", "APP_TAXI_CUSTOMER_OPTIONS": "Options", "APP_TAXI_CUSTOMER_OPTIONS_CANCEL": "Cancel", "APP_TAXI_TIP_BILLING_DESC": "Taxi Tip"}, "MusicAPP": {"APP_MUSIC_HELLO": "Hello, {name}!", "APP_MUSIC_HOME_DESC": "Let's listen to something great today", "APP_MUSIC_PLAYLIST": "Playlist", "APP_MUSIC_PLAYLIST_EMPTY": "The playlist is empty! How about filling it with your favorite songs? 🎤", "APP_MUSIC_LIBRARY": "Your Library", "APP_MUSIC_LIKED_SONGS": "Liked Songs", "APP_MUSIC_PLAYLISTDETAILS_SONGS_LENGTH": "{length} songs", "APP_MUSIC_CANCEL": "Cancel", "APP_MUSIC_DONE": "Done", "APP_MUSIC_EDIT_PLAYLIST": "Edit Playlist", "APP_MUSIC_DELETE_PLAYLIST": "Delete Playlist", "APP_MUSIC_NEW_PLAYLIST": "New Playlist", "APP_MUSIC_PLAYLIST_NAME": "Playlist Name", "APP_MUSIC_PLAYLIST_IMG": "Playlist Image", "APP_MUSIC_PRELOADER_MUSIC_DELETE": "Deleting song...", "APP_MUSIC_PLAYLIST_DELETE_SUCCESS": "Playlist deleted successfully", "APP_MUSIC_LISTEN": "What do you want to listen to? 🎧", "APP_MUSIC_PLAYTHIS_SONG": "Play this song", "APP_MUSIC_ADD_QUEUE": "Add to Queue", "APP_MUSIC_ADD_PLAYLIST": "Add to Playlist", "APP_MUSIC_ADD_LIKED": "Add to Liked Songs", "APP_MUSIC_REMOVE_LIKED": "Remove from Liked Songs", "APP_MUSIC_SEARCHING": "Searching...", "APP_MUSIC_ADDING_PLAYLIST": "Adding to playlist...", "APP_MUSIC_ADD_PLAYLIST_SUCCESS": "Successfully added to playlist", "APP_MUSIC_ADD_QUEUE_SUCCESS": "Successfully added to queue", "APP_MUSIC_SEARCH_PLACEHOLDER": "Search", "APP_ALERT_STREAM_MODE": "You can't listen to music while streaming", "APP_ALERT_STREAM_MODE_TITLE": "Stream Mode"}, "JobCenter": {"APP_JOBCENTER_TITLE": "Job Center", "APP_JOBCENTER_WELCOME": "Welcome, {name}", "APP_JOBCENTER_WELCOME_DESC": "Find your job!", "APP_JOBCENTER_CREATEGROUP": "Create Group", "APP_JOBCENTER_WAYPOINT": "Waypoint", "APP_JOBCENTER_GROUPLIST": "Groups", "APP_JOBCENTER_GROUPMEMBERS": "Group Members", "APP_JOBCENTER_TASKLIST": "Tasks", "APP_JOBCENTER_ONLINE": "Online", "APP_JOBCENTER_OFFLINE": "Offline", "APP_JOBCENTER_READYGROUP": "Ready Group", "APP_JOBCENTER_PASSWORD_PLACEHOLDER": "Password", "APP_JOBCENTER_BUTTON_JOIN": "Join", "APP_JOBCENTER_BUTTON_LEAVEGROUP": "Leave", "APP_JOBCENTER_BUTTON_DELETEGROUP": "Disband Group", "APP_JOBCENTER_BUTTON_CLOSE": "Close", "APP_JOBCENTER_BUTTON_CREATE": "Create", "APP_JOBCENTER_TOAST_WAYPOINT_SET": "Waypoint set", "APP_JOBCENTER_LOADING_CREATE_GROUP": "Creating group...", "APP_JOBCENTER_TOAST_CREATE_GROUP_SUCCESS": "Group successfully created", "APP_JOBCENTER_TOAST_CREATE_GROUP_ERROR": "Error creating group", "APP_JOBCENTER_TOAST_GROUPFULL": "Group is full", "APP_JOBCENTER_TOAST_INVALIDPASSWORD": "Invalid password", "APP_JOBCENTER_LOADING_REQUEST": "Sending request...", "APP_JOBCENTER_TOAST_REQUESTSENT": "Request sent", "APP_JOBCENTER_TOAST_REQUESTFAILED": "Error sending request", "APP_JOBCENTER_POUP_GROUP_PASSWORD": "Group Password", "APP_JOBCENTER_POUP_GROUP_PASSWORD_DESC": "Enter the group password", "APP_JOBCENTER_LOADING_LEAVE_GROUP": "Leaving group...", "APP_JOBCENTER_TOAST_LEAVE_GROUP_SUCCESS": "Successfully left group", "APP_JOBCENTER_TOAST_LEAVE_GROUP_ERROR": "Error leaving group", "APP_JOBCENTER_LOADING_GROUP_STATUS": "Updating group status...", "APP_JOBCENTER_TOAST_GROUP_STATUS_SUCCESS": "Group status updated", "APP_JOBCENTER_TOAST_GROUP_STATUS_ERROR": "Error updating group status", "APP_JOBCENTER_LOADING_KICK_MEMBER": "Removing member...", "APP_JOBCENTER_TOAST_KICK_MEMBER_SUCCESS": "Member removed", "APP_JOBCENTER_TOAST_KICK_MEMBER_ERROR": "Error removing member", "APP_JOBCENTER_LOADING_DISBAND_GROUP": "Disbanding group...", "APP_JOBCENTER_TOAST_DISBAND_GROUP_SUCCESS": "Group disbanded", "APP_JOBCENTER_TOAST_DISBAND_GROUP_ERROR": "Error disbanding group", "APP_JOBCENTER_NOTIFY_JOBSUCESS_REWARD": "You have successfully completed the task. You have earned $ {reward}", "APP_JOBCENTER_NOTIFY_REQUEST": "Do you want to accept the job participation request?", "APP_JOBCENTER_NOTIFY_FOUNDJOB": "A job has been found, do you want to accept it?"}, "Casino": {"APP_CASINO_TITLE": "Casino", "APP_CASINO_CATEGORY": "Category", "APP_CASINO_DICE": "<PERSON><PERSON>", "APP_CASINO_ROLL": "Roll", "APP_CASINO_ROLL_ROLLINGIN": "Rolling in...", "APP_CASINO_ROLL_WINNER": "WINNER", "APP_CASINO_ROLL_WIN": "Win:", "APP_CASINO_ROLL_PLACEBET": "Place Bet", "APP_CASINO_ROLL_TOTALBET": "Total Bet: ", "APP_CASINO_ROLL_GAMENOTSTARTED": "Game not started yet", "APP_CASINO_ROLL_NOTBALANCE": "You do not have enough balance", "APP_CASINO_DICE_BETAMOUNT": "Bet Amount:", "APP_CASINO_DICE_ROOMCLOSE": "Room Close", "APP_CASINO_DICE_GAMESTARTED": "Game Started", "APP_CASINO_DICE_STARTGAME": "Start Game", "APP_CASINO_DICE_JOINGAME": "Join Game", "APP_CASINO_DICE_LEAVEGAME": "Leave Game", "APP_CASINO_DICE_GAMENOTSTARTED": "Game not started yet", "APP_CASINO_DICE_WINNER": "Winner", "APP_CASINO_DICE_TURN": "{turn} Turn", "APP_CASINO_DICE_SCOREBOARD": "Scoreboard", "APP_CASINO_DICE_ISGAMESTARTED": "Game is already started", "APP_CASINO_DICE_LEFTTHEGAME": "Left the game", "APP_CASINO_DICE_JOINEDTHEGAME": "Joined the game", "APP_CASINO_DICE_DICEROOMS": "Dice Rooms", "APP_CASINO_DICE_JOIN": "Join", "APP_CASINO_DICE_CREATINGROOM": "Creating room...", "APP_CASINO_DICE_JOININGROOM": "Joining room...", "APP_CASINO_DICE_CREATEERROR": "An error occurred while creating the room. Please try again later.", "APP_CASINO_DICE_JOINERROR": "An error occurred while joining the room. Please try again later.", "APP_CASINO_WALLET_ERROR": "An error occurred while buying or sell balance.", "APP_CASINO_CHIP_SUCCESS": "Chip bought successfully", "APP_CASINO_CHIP_NOTENOUGH": "You don't have enough money", "APP_CASINO_CHIP_SELL": "Chip sell successfully", "APP_CASINO_CHIP_SELL_ERROR": "You don't have enough chips", "APP_CASINO_CHIP_BANK_BUY": "Chip Buy", "APP_CASINO_CHIP_BANK_SELL": "<PERSON>", "APP_CASINO_NAME_FAILED": "Name change failed", "APP_CASINO_NAME_SUCCESS": "Name changed successfully", "APP_CASINO_CREATEROOM_FAILED": "Room creation failed", "APP_CASINO_CREATEROOM_SUCCESS": "Room created successfully", "APP_CASINO_JOINROOM_SUCCESS": "Room joined successfully", "APP_CASINO_JOINROOM_PASSWORDWRONG": "Room password is wrong", "APP_CASINO_JOINROOM_NOTOPEN": "Room is not open", "APP_CASINO_JOINROOM_NOROOM": "Room not found", "APP_CASINO_JOINGAME_FAILED": "Game join failed", "APP_CASINO_JOINGAME_SUCCESS": "Game joined successfully", "APP_CASINO_JOINGAME_CHIPENOT": "You don't have enough chips", "APP_CASINO_JOINGAME_NOTINROOM": "You are not in the room", "APP_CASINO_JOINGAME_NOTOPENGAME": "Game is not open", "APP_CASINO_LEAVEGAME_FAILED": "Game leave failed", "APP_CASINO_LEAVEGAME_SUCCESS": "Game left successfully", "APP_CASINO_LEAVEGAME_NOTINGAME": "You are not in the game", "APP_CASINO_GAMESTART_FAILED": "Game start failed", "APP_CASINO_GAMESTART_SUCCESS": "Game started successfully", "APP_CASINO_GAMESTART_NOTPLAYER": "Player not found", "APP_CASINO_GAMESTART_NOTLEADER": "You are not the leader of this room", "APP_CASINO_GAMECLOSE_FAILED": "Game close failed", "APP_CASINO_GAMECLOSE_SUCCESS": "Game closed successfully", "APP_CASINO_GAMECLOSE_NOTLEADER": "You are not the leader of this room", "APP_CASINO_ROLL_BET_FAILED": "Bet failed", "APP_CASINO_ROLL_BET_SUCCESS": "Bet successfully", "APP_CASINO_ROLL_BET_INVALID": "Invalid bet amount", "APP_CASINO_ROLL_BET_INVALIDDATA": "Invalid data", "APP_CASINO_ROLL_WINNER_FAILED": "Winner failed", "APP_CASINO_ROLL_WINNER_SUCCESS": "Winner", "APP_CASINO_ROLL_WINNER_LOST": "You lost", "APP_CASINO_ROLL_WINNER_NOTBET": "You didn't bet", "APP_CASINO_ROLL_BET_MAX": "You can't bet more than {max} chips", "APP_CASINO_MODAL": {"WALLET": "Wallet", "NICKNAME": "Nickname", "NICKNAME_TEXT": "The nickname you set will remain the same until you close the app and open it again.", "NICKNAME_BUTTONSAVE": "Save", "NICKNAME_CHANGE_ERROR": "An error occurred while changing the nickname.", "PLACE_BET": "Place Bet", "PLACE_BUTTONTEXT": "Place", "CRATE_DICEROOM": "Create Dice Room", "CRATE_DICEROOM_NAME_PLACEHOLDER": "Room Name", "CRATE_DICEROOM_PASSWORD": "Password (Optional)", "CRATE_DICEROOM_PASSWORD_PLACEHOLDER": "Password", "CRATE_DICEROOM_NICKNAME_PLACEHOLDER": "Nickname", "CRATE_DICEROOM_BETAMOUNT_PLACEHOLDER": "Bet Amount", "CRATE_DICEROOM_TEXT": "You can specify the details of the room you want to create below.", "CRATE_DICEROOM_BUTTONTEXT": "Create Room", "JOIN_ROOM": "Join Room #{room}", "JOIN_ROOM_TEXT": "You join the room with the name  '<span style='color: #21A48C;'>{name}</span>' that you specified earlier.", "JOIN_ROOM_BUTTONTEXT": "Join", "YOU_BALANCE": "You have {balance} chip.", "BET_AMOUNT": "Bet Amount:", "TRANSFERCHIP": "Transfer Chip", "BUYCHIP": "Buy Chip", "TAX": "Tax: {tax}%", "YOU_BALANCECHIP": "You have {balance}", "BUYAMAOUNTTEXT": "Buy Amount:", "TRANSFERAMOUNTTEXT": "Transfer Amount:", "TOTAL_TEXT": "Total =", "MINIMUMTRANSFER": "Minumum Transfer:", "BUY_TEXT": "Buy", "TRANSFER_TEXT": "Transfer", "TRENSFER_ERROR1": "You can not transfer less than 250 chip.", "TRENSFER_ERROR2": "You do not have enough balance."}}, "Info": {"APP_INFO_PERSONALINFO": "Personal Information", "APP_INFO_PHONENUMBER": "Phone Number", "APP_INFO_BIRTHDATE": "Birth Date", "APP_INFO_BANKACCOUNT": "Bank Account", "APP_INFO_JOB": "Job", "APP_INFO_GRADE": "Grade", "APP_INFO_LICENSES": "Licenses"}, "FlareApp": {"APP_FLARE_TITLE": "Flare", "APP_FLARE_LOADING": "Loading...", "APP_FLARE_NO_CARDS": "No more cards available", "APP_FLARE_SWIPE_DESCRIPTION": "Swipe right to like or left to dislike", "APP_FLARE_START_SWIPE": "Start Swiping", "APP_FLARE_MATCHES_APPEAR": "Your matches will appear here.", "APP_FLARE_NEW_MATCHES": "NEW MATCHES", "APP_FLARE_MESSAGES": "MESSAGES", "APP_FLARE_DATA_POLICY": "By clicking Create Account, you agree to our Terms and that you have read our Data Policy, including our Cookie Use.", "APP_FLARE_CREATE_ACCOUNT": "CREATE ACCOUNT", "APP_FLARE_YOUR_NAME": "What's your name?", "APP_FLARE_YOUR_NAME_PLACEHOLDER": "Enter first name", "APP_FLARE_YOUR_NAME_DESC": "3 characters minimum and 10 characters maximum", "APP_FLARE_NEXT": "Next", "APP_FLARE_YOUR_BIRTHDAY": "Your b-day?", "APP_FLARE_YOUR_BIRTHDAY_DESC": "Your profile shows your age, not your date of birth.", "APP_FLARE_YOUR_BIRTHDAY_DESC2": "18 years old minimum", "APP_FLARE_YOUR_GENDER": "What's your gender?", "APP_FLARE_GENDER_WOMAN": "Woman", "APP_FLARE_GENDER_MAN": "Man", "APP_FLARE_SEEING": "Who are you interested in seeing?", "APP_FLARE_SEEING_WOMEN": "Women", "APP_FLARE_SEEING_MEN": "Men", "APP_FLARE_SEEING_EVERYONE": "Everyone", "APP_FLARE_ADD_PHOTO": "Add your recent pics", "APP_FLARE_YOUR_ABOUT": "Tell us a bit about yourself", "APP_FLARE_YOUR_ABOUT_PLACEHOLDER": "Write something about yourself", "APP_FLARE_CREATING_ACCOUNT": "Creating your account...", "APP_FLARE_PHOTOS_AND_VIDEOS": "Photos and Videos", "APP_FLARE_NAME": "Name", "APP_FLARE_ABOUT_ME": "About Me", "APP_FLARE_SHOW_ME": "Show Me", "APP_FLARE_GENDER": "Gender", "APP_FLARE_SAVE": "Save", "APP_FLARE_DELETE_ACCOUNT": "Delete Account", "APP_FLARE_DELETE_ACCOUNT_DESC": "Are you sure you want to delete your account? This action cannot be undone.", "APP_FLARE_SAVE_PROFILE_DESC": "Are you sure you want to save your profile? You can change it later in the settings.", "APP_FLARE_PROFILE_UPDATED": "Profile updated successfully", "APP_FLARE_SEARCH": "Search", "APP_FLARE_NEWMATCH": "New Match", "APP_FLARE_NOTIFY_MATCH": "You have a new match with {name}", "APP_FLARE_BACK": "Back", "APP_FLARE_IMAGE_NOTLOAD": "Image not loaded", "APP_FLARE_IMAGE_NOTLOAD_DESC": "Image file not found <br> or link is broken"}, "Snapgram": {"APP_SNAPGRAM_TITLE": "Snapgram", "APP_SNAPGRAM_USERNAME": "Username", "APP_SNAPGRAM_PASSWORD": "Password", "APP_SNAPGRAM_LOGIN": "<PERSON><PERSON>", "APP_SNAPGRAM_FULLNAME": "Full Name", "APP_SNAPGRAM_CREATE_ACCOUNT": "Create New Account", "APP_SNAPGRAM_CREATE_USERNAME": "Create a Username", "APP_SNAPGRAM_CREATE_PASSWORD": "Create a Password", "APP_SNAPGRAM_CREATE_FULL_NAME": "What's your full name?", "APP_SNAPGRAM_CREATE_PICTURE": "Add a profile picture", "APP_SNAPGRAM_CREATE_FINISH": "Account created!", "APP_SNAPGRAM_CREATE_USERNAME_DESC": "Add a username to your account. You can change it later.", "APP_SNAPGRAM_CREATE_PASSWORD_DESC": " Add a password to your account. You can change it later.", "APP_SNAPGRAM_CREATE_FULL_NAME_DESC": "Add your name so that your friends can find you on Snapgram.", "APP_SNAPGRAM_CREATE_PICTURE_DESC": "Add a profile picture to your account. You can change it later.", "APP_SNAPGRAM_ADD_PICTURE": "Add Picture", "APP_SNAPGRAM_CREATE_FINISH_DESC": "Your account is ready! You can now start using Snapgram.", "APP_SNAPGRAM_SKIP": "<PERSON><PERSON>", "APP_SNAPGRAM_FINISH": "Finish", "APP_SNAPGRAM_FULLNAME_ERROR": "Full name must be between 3 and 30 characters.", "APP_SNAPGRAM_PASSWORD_ERROR": "Password must be between 6 and 30 characters.", "APP_SNAPGRAM_PASSWORD_REQUIREMENTS": "Password must contain at least one letter and one number.", "APP_SNAPGRAM_PASSWORD_SPACE_ERROR": "Password cannot contain spaces.", "APP_SNAPGRAM_USERNAME_ERROR": "Username must be between 3 and 30 characters.", "APP_SNAPGRAM_USERNAME_REQUIREMENTS": "Username can only contain letters, numbers, dots, and underscores.", "APP_SNAPGRAM_USERNAME_SPACE_ERROR": "Username cannot contain spaces.", "APP_SNAPGRAM_CHECKING_USERNAME": "Checking username...", "APP_SNAPGRAM_USERNAME_EXISTS": "Username already exists. Please choose another one.", "APP_SNAPGRAM_CREATING_ACCOUNT": "Creating your account...", "APP_SNAPGRAM_ACCOUNT_CREATED": "Account created successfully!", "APP_SNAPGRAM_ACCOUNT_CREATION_FAILED": "Failed to create account. Please try again", "APP_SNAPGRAM_LOGIN_ERROR": "Please enter your username and password", "APP_SNAPGRAM_LOGGING_IN": "Logging in...", "APP_SNAPGRAM_LOGIN_SUCCESS": "Login successful!", "APP_SNAPGRAM_LOGIN_FAILED": "<PERSON><PERSON> failed. Please check your username and password.", "APP_SNAPGRAM_NEXT": "Next", "APP_SNAPGRAM_YOUR_STORY": "Your Story", "APP_SNAPGRAM_NO_FOLLOWED_ANYONE": "You haven't followed anyone yet.", "APP_SNAPGRAM_STORY_ADDED": "Story added successfully!", "APP_SNAPGRAM_DELETING_STORY": "The story is being deleted...", "APP_SNAPGRAM_STORY_DELETED": "Story deleted successfully!", "APP_SNAPGRAM_STORY_DELETE_FAILED": "The story could not be deleted.", "APP_SNAPGRAM_NEWPOST_TITLE": "New Post", "APP_SNAPGRAM_DONE": "Done", "APP_SNAPGRAM_SHARE": "Share", "APP_SNAPGRAM_NO_IMAGES_SELECTED": "No images selected", "APP_SNAPGRAM_GALLERY": "Gallery", "APP_SNAPGRAM_ADD_EXPLANATION": "Add an explanation ...", "APP_SNAPGRAM_ADD_LOCATION": "Add location", "APP_SNAPGRAM_ENTER_LOCATION": "Enter a place name to add a location:", "APP_SNAPGRAM_LOCATION_ADDED": "The location was added {location}.", "APP_SNAPGRAM_LOCATION_EMPTY": "Location cannot be empty.", "APP_SNAPGRAM_SHARED": "Shared ...", "APP_SNAPGRAM_SELECT_MIN_PHOTO": "Please select at least one photo.", "APP_SNAPGRAM_SELECT_MAX_PHOTO": "You can share a maximum of 10 photos.", "APP_SNAPGRAM_CAPTION_MIN": "The description must be at least 5 characters.", "APP_SNAPGRAM_LOCATION_MAX": "The location description must be up to 250 characters.", "APP_SNAPGRAM_SHARE_SUCCESS": "Sharing Successful!", "APP_SNAPGRAM_SHARE_FAILED": "Sharing failed. Please try again.", "APP_SNAPGRAM_CHOOSE_PHOTO": "Please choose a photo.", "APP_SNAPGRAM_ACTIVITY": "Activity", "APP_SNAPGRAM_FOLLOWER_REQUESTS": "Follower requests", "APP_SNAPGRAM_LIKED_POST": "liked your post", "APP_SNAPGRAM_COMMENTED_POST": "commented on your post", "APP_SNAPGRAM_STARTED_FOLLOWING": "started following you", "APP_SNAPGRAM_FOLLOWING": "Following", "APP_SNAPGRAM_FOLLOW": "Follow", "APP_SNAPGRAM_NO_ACTIVITY": "You don’t have any activity.", "APP_SNAPGRAM_FOLLOW_REQUEST_ERROR": "An error occurred while sending the follow request.", "APP_SNAPGRAM_FOLLOWED_SUCCESS": "Followed successfully.", "APP_SNAPGRAM_UNFOLLOWED_SUCCESS": "Unfollowed successfully.", "APP_SNAPGRAM_REQUEST_SENT_SUCCESS": "Follow request sent successfully.", "APP_SNAPGRAM_SENDING_MESSAGE": "Sending message…", "APP_SNAPGRAM_PLACEHOLDER_MESSAGE": "Message", "APP_SNAPGRAM_PLACEHOLDER_ADD_COMMENT_SEND": "Add comment or send", "APP_SNAPGRAM_SHEET_GIF": "GIF", "APP_SNAPGRAM_CLOSE": "Close", "APP_SNAPGRAM_FOLLOWERS": "Followers", "APP_SNAPGRAM_NEW_MESSAGES": "{count}+ new messages", "APP_SNAPGRAM_SENT_IMAGE": "Image sent", "APP_SNAPGRAM_SENT_VIDEO": "Video sent", "APP_SNAPGRAM_SEARCHBAR_PLACEHOLDER": "Search", "APP_SNAPGRAM_SEARCHBAR_CANCEL": "Cancel", "APP_SNAPGRAM_MESSAGE_DELETE": "Delete Message", "APP_SNAPGRAM_MESSAGE_DELETED": "The message was deleted.", "APP_SNAPGRAM_NO_SEARCH_RESULTS": "There are no new posts for you.", "APP_SNAPGRAM_POSTS": "Posts", "APP_SNAPGRAM_PRIVATE_ACCOUNT": "Private Account", "APP_SNAPGRAM_EDIT_PROFILE": "Edit Profile", "APP_SNAPGRAM_MESSAGE": "Message", "APP_SNAPGRAM_CHANGE_PROFILE_PICTURE": "Change Profile Picture", "APP_SNAPGRAM_BIO": "Bio", "APP_SNAPGRAM_SIGN_OUT": "Sign Out", "APP_SNAPGRAM_CHANGE_PASSWORD": "Change Password", "APP_SNAPGRAM_DELETE_ACCOUNT": "Delete Account", "APP_SNAPGRAM_ADD_ACCOUNT": "Add Account", "APP_SNAPGRAM_FOLLOW_REQUEST_SENT_LABEL": "Follow Request Sent", "APP_SNAPGRAM_DELETE_ACCOUNT_DESC": "Are you sure you want to delete your account?", "APP_SNAPGRAM_DELETE_ACCOUNT_TITLE": "Delete Account", "APP_SNAPGRAM_DELETING_ACCOUNT": "Deleting account...", "APP_SNAPGRAM_ACCOUNT_DELETED": "Account deleted successfully.", "APP_SNAPGRAM_ADD_ACCOUNT_DESC": "Are you sure you want to add a new account?", "APP_SNAPGRAM_ADDING_ACCOUNT": "Adding account...", "APP_SNAPGRAM_MESSAGE_ERROR": "You need to select a user to send a message.", "APP_SNAPGRAM_SWITCH_ACCOUNT": "Switch Account", "APP_SNAPGRAM_SWITCH_ACCOUNT_DESC": "Are you sure you want to switch accounts?", "APP_SNAPGRAM_SWITCHING_ACCOUNT": "Switching account...", "APP_SNAPGRAM_ACCOUNT_SWITCHED": "Account switched successfully.", "APP_SNAPGRAM_QUICK_ACC_DEL_DESC": "Are you sure you want to remove it from quick access?", "APP_SNAPGRAM_SIGN_OUT_DESC": "Are you sure you want to sign out?", "APP_SNAPGRAM_SIGNED_OUT": "Signed out successfully.", "APP_SNAPGRAM_UPDATE_ERROR_PROFILE": "You need to be logged in to update your profile.", "APP_SNAPGRAM_UPDATE_BIO_ERROR": "B<PERSON> must be less than 200 characters.", "APP_SNAPGRAM_UPDATE_PROFILE_SUCCESS": "Profile updated successfully.", "APP_SNAPGRAM_UPDATE_PROFILE_ERROR": "An error occurred while updating the profile.", "APP_SNAPGRAM_CHANGE_PASSWORD_DESC": "Are you sure you want to change your password? Specify your current password", "APP_SNAPGRAM_PASSWORD_CURRENT_INCORRECT": "Current password is incorrect.", "APP_SNAPGRAM_PASSWORD_NEW_PASSWORD": "Please fill out your new SnapGram password", "APP_SNAPGRAM_PASSWORD_SUCCESS": "Password changed successfully.", "APP_SNAPGRAM_PASSWORD_UNSUCCESS": "An error occurred while changing the password.", "APP_SNAPGRAM_CURRENT_REQUIRED": "Current password is required.", "APP_SNAPGRAM_PROFILE_NOT_FOUND_DESC": "The profile you are looking for does not exist or has been deleted.", "APP_SNAPGRAM_COMMENTS": "Comments", "APP_SNAPGRAM_DELETE_COMMENT_CONFIRM": "Are you sure you want to delete this comment?", "APP_SNAPGRAM_DELETE": "Delete", "APP_SNAPGRAM_COMMENT_DELETING": "Comment is being deleted...", "APP_SNAPGRAM_COMMENT_DELETED_SUCCESS": "Comment deleted successfully.", "APP_SNAPGRAM_COMMENT_DELETE_ERROR": "An error occurred while deleting the comment.", "APP_SNAPGRAM_DELETE_POST_CONFIRM": "Are you sure you want to delete this post?", "APP_SNAPGRAM_POST_DELETING": "Post is being deleted...", "APP_SNAPGRAM_POST_DELETED_SUCCESS": "The post was deleted successfully.", "APP_SNAPGRAM_POST_DELETE_ERROR": "An error occurred while deleting the post.", "APP_SNAPGRAM_SENDING_COMMENT": "Sending comment...", "APP_SNAPGRAM_REPLY": "Reply", "APP_SNAPGRAM_DELETE_COMMENT": "Delete Comment", "APP_SNAPGRAM_SEE_ANSWER": "See the answer", "APP_SNAPGRAM_HIDE_ANSWER": "Hide the answer", "APP_SNAPGRAM_BACK": "Back", "APP_SNAPGRAM_REPLY_TO": "Reply to {username}.", "APP_SNAPGRAM_LEAVE_COMMENT_PLACEHOLDER": "Leave a comment...", "APP_SNAPGRAM_SEND": "Send", "APP_SNAPGRAM_NEW_POST_SHARE": "{userName} shared a new post", "APP_SNAPGRAM_NEW_MESSAGE": "{user<PERSON><PERSON>} sent you a new message", "APP_SNAPGRAM_NOTIF_LIKE": "{user<PERSON><PERSON>} liked your post", "APP_SNAPGRAM_NOTIF_COMMENT": "{userName} commented on your post", "APP_SNAPGRAM_NOTIF_COMMENT_LIKE": "{user<PERSON><PERSON>} liked your comment", "APP_SNAPGRAM_NOTIF_FOLLOW": "{userName} started following you", "APP_SNAPGRAM_NOTIF_FOLLOW_REQUEST": "{userName} sent you a follow request", "APP_SNAPGRAM_NOTIF_FOLLOW_REQUEST_ACCEPTED": "{userName} accepted your follow request"}, "Tooltip": {"ESIM": {"APP_TOOLTIP_SIM_BUY": "Buy"}, "Message": {"APP_TOOLTIP_MESSAGE_CREATEGROUPMEMBER": "{'@'}Contact Name"}, "Advertising": {"APP_TOOLTIP_ADVERTISING_POST": "New Post", "APP_TOOLTIP_ADVERTISING_POSTCALL": "Call", "APP_TOOLTIP_ADVERTISING_POSTCALL2": "Anonymous Call", "APP_TOOLTIP_ADVERTISING_POSTMESSAGE": "Message", "APP_TOOLTIP_ADVERTISING_POSTFAV": "Favorite", "APP_TOOLTIP_ADVERTISING_REPOST": "Repost", "APP_TOOLTIP_ADVERTISING_DELETE": "Delete"}, "Squawk": {"APP_TOOLTIP_SQUAWK_COMMENT": "Comment", "APP_TOOLTIP_SQUAWK_REPOST": "Repost", "APP_TOOLTIP_SQUAWK_LIKE": "Like", "APP_TOOLTIP_SQUAWK_POST_IMAGE": "Image", "APP_TOOLTIP_SQUAWK_POST_VIDEO": "Video", "APP_TOOLTIP_SQUAWK_POST_POLL": "Poll", "APP_TOOLTIP_SQUAWK_POST_VOICE": "Voice Message", "APP_TOOLTIP_SQUAWK_POST_EMOJI": "<PERSON><PERSON><PERSON>"}, "CarSeller": {"APP_TOOLTIP_CARSELLER_CALL": "Call", "APP_TOOLTIP_CARSELLER_MESSAGE": "Message", "APP_TOOLTIP_CARSELLER_BUY": "Buy", "APP_TOOLTIP_CARSELLER_DELETE": "Delete"}, "Music": {"APP_TOOLTIP_MUSIC_HOME": "Home", "APP_TOOLTIP_MUSIC_SEARCH": "Search", "APP_TOOLTIP_MUSIC_LIBRARY": "Library", "APP_TOOLTIP_MUSIC_REMOVELIKE": "Dislike", "APP_TOOLTIP_MUSIC_LIKE": "Like", "APP_TOOLTIP_MUSIC_PLAY": "Play", "APP_TOOLTIP_MUSIC_PAUSE": "Pause", "APP_TOOLTIP_MUSIC_ADD": "Add", "APP_TOOLTIP_MUSIC_REMOVE": "Remove", "APP_TOOLTIP_MUSIC_DETAIL": "Details", "APP_TOOLTIP_MUSIC_PLAYLISTS": "Playlists", "APP_TOOLTIP_CREATE_PLAYLIST": "Create Playlist", "APP_TOOLTIP_MUSIC_BACK": "Back", "APP_TOOLTIP_MUSIC_NEXT": "Next", "APP_TOOLTIP_MUSIC_REPEAT": "Repeat", "APP_TOOLTIP_MUSIC_SHUFFLE": "Shuffle", "APP_TOOLTIP_MUSIC_EDIT_PLAYLIST": "Edit Playlist", "APP_TOOLTIP_MUSIC_EARPHONE": "Earphone", "APP_TOOLTIP_MUSIC_SPEAKER": "Speaker"}}, "LucyAI": {"APP_LUCYAI_TITLE": "Lucy AI", "APP_LUCYAI_TALK": "Talk to <PERSON>", "APP_LUCYAI_NOTPERMISSION": "Microphone permission not granted for <PERSON>, please grant permission.", "APP_LUCYAI_VOICEPROGRESS": "Voice recognition in progress...", "APP_LUCYAI_NOTUNDERSTAND": "Not understood, please try again.", "APP_LUCYAI_NOTLANGUAGE_SELECTED": "No language selected, please select a language in settings.", "APP_LUCYAI_PERSONNOTFOUND": "{contact<PERSON><PERSON>} not found in the address book", "APP_LUCYAI_APPNOTFOUND": "{appName} application not found, please install the application.", "APP_LUCYAI_COMMANDS": "Lucy AI Commands", "APP_LUCYAI_LANGUAGES": "Languages", "APP_LUCYAI_VOICES": "Voices", "CommandsList": {"APP_LUCYAI_COMMANDS_CALL": "Call", "APP_LUCYAI_COMMANDS_TAKESCREENSHOT": "Take Screenshot", "APP_LUCYAI_COMMANDS_OPENAPP": "Open App", "APP_LUCYAI_COMMANDS_MONEYTRANSFER": "Money Transfer"}}, "RegisterCommand": {"REGISTERCOMMAND_OPENPHONE": "Open Phone", "REGISTERCOMMAND_NORMALCHARGE": "Normal Charge", "REGISTERCOMMAND_FASTCHARGE": "Fast Charge", "REGISTERCOMMAND_PHONEBOOTH": "Phone Booth", "REGISTERCOMMAND_ANSWERCALL": "Answer Call", "REGISTERCOMMAND_DECLINECALL": "Decline Call"}}
Shells = {
    ["container_shell"] = vec4(-0.05, -5.49, -0.21, 0.49),
    ["furnitured_midapart"] = vec4(1.37, -10.19, -0.52, 8.95),
    ["standardmotel_shell"] = vec4(-0.47, -2.42, -0.56, 265.99),
    ["modernhotel_shell"] = vec4(4.95, 4.15, -0.82, 179.11),
    ["shell_frankaunt"] = vec4(-0.34, -5.84, -0.57, 357.89),
    ["shell_garagem"] = vec4(13.31, 1.5, -0.75, 86.97),
    ["shell_lester"] = vec4(-1.62, -5.98, -0.37, 1.26),
    ["shell_michael"] = vec4(-9.67, 5.65, -3.99, 270.8),
    ["shell_office1"] = vec4(1.19, 5.04, -0.73, 175.2),
    ["shell_ranch"] = vec4(-1.19, -5.35, -1.1, 264.19),
    ["shell_store1"] = vec4(-2.76, -4.7, -0.62, 5.84),
    ["shell_trailer"] = vec4(-1.34, -2.01, -0.48, 0.29),
    ["shell_trevor"] = vec4(0.2, -3.85, -0.41, 358.44),
    ["shell_v16low"] = vec4(4.77, -6.46, -1.65, 1.46),
    ["shell_v16mid"] = vec4(1.52, -14.34, -0.49, 358.45),
    ["shell_warehouse1"] = vec4(-8.95, 0.03, -0.95, 267.48),

    -- Addon shells
    -- Envi Shells Starter Pack
    -- ["envi_shell_01_empty"] = vec4(0.600220, 0.597168, -1.127975, 2.26),
    -- ["envi_shell_02_empty"] = vec4(0.072021, -10.788818, 0.038795,  356.032074),
    -- ["envi_shell_03_empty"] = vec4(5.004517, -6.798584, 0.192665,  92.380592),
    -- ["envi_shell_01_furnished"] = vec4(0.390381, 0.518066, -1.128654,  2.298164),
    -- ["envi_shell_02_furnished"] = vec4(0.123047, -11.069580, 0.045692,  358.241425),
    -- ["envi_shell_03_furnished"] = vec4(4.955200, 1.096436, 0.194565,  87.552078),

    -- Max Creation Shells
    -- ['furnished_shell'] = vec4(-2.63232421875, 6.7724609375, 3.1604804992676, 185.17088317871),
    -- ['newfurnished_shell'] = vec4(-16.715759277344, -6.133544921875, 1.8005065917969, 270.59631347656),
    -- ['max_row_house'] = vec4(-2.4906005859375, -6.9010620117188, 0.55683898925781, 357.31881713867),
    -- ['lc_appartment'] = vec4(0.6007080078125, -5.67529296875, -3.1005821228027, 358.42181396484),
    -- ['loft_shell1'] = vec4(-1.3081665039063, 8.8837890625, 1.0293273925781, 175.15191650391),
    -- ['customloft_shell2'] = vec4(2.0274047851563, -6.3671875, 0.71349334716797, 357.4362487793),
    -- ['small_house1'] = vec4(2.7097778320313, 7.197998046875, -0.36474609375, 185.40277099609),
    -- ['max_villa2'] = vec4(4.3534545898438, -12.241943359375, -3.529972076416, 274.69995117188),
    -- ['maxcreations_mediumend'] = vec4(2.9427490234375, -2.68505859375, -0.39791870117188, 0.086746469140053),
    -- ['maxcreations_small'] = vec4(2.55078125, 2.97314453125, -0.39087295532227, 176.53344726563),
    -- ['unfurnished_shell'] = vec4(-2.7021484375, 6.72265625, 3.1601982116699, 181.83374023438),
    -- ['newunfurnished_shell'] = vec4(-16.643981933594, -5.98828125, 1.8005981445313, 274.06195068359),
    -- ['lc_appartmentuf'] = vec4(0.47637939453125, -5.41259765625, -3.0990905761719, 0.78879874944687),
    -- ['loft_shell1uf'] = vec4(-1.3971557617188, 8.943115234375, 1.0292739868164, 178.6595916748),
    -- ['customloft_shell2uf'] = vec4(2.1945190429688, -6.369140625, 0.71345520019531, 1.0491790771484),
    -- ['max_row_houseuf'] = vec4(-2.4708862304688, -7.39599609375, 0.55685043334961, 356.99749755859),

    -- K4MB1 Shells
    -- Thanks to dr.ayushabhinav

    -- ["k4mb1_apa1_shell"] = vec4(-19.15, -0.43, 1.79, 0.0),
    -- ["k4mb1_apa2_shell"] = vec4(-19.24, -0.31, 1.79, 0.0),
    -- ["k4mb1_apa3_shell"] = vec4(-19.2, -0.32, 1.79, 0.0),
    -- ["k4mb1_classic4_shell"] = vec4(4.76, -2.17, -3.38, 270.00),
    -- ["k4mb1_classic5_shell"] = vec4(4.80, -2.06, -3.38, 270.00),
    -- ["k4mb1_classic6_shell"] = vec4(4.85, -2.08, -3.38, 270.00),
    -- ["k4mb1_loft1_shell"] = vec4(6.71, -2.97, 0.99, 279.00),
    -- ["k4mb1_loft2_shell"] = vec4(8.63, 1.02, 0.99, 270.31),
    -- ["k4mb1_loft3_shell"] = vec4(5.82, -0.39, 0.99, 270.31),
    -- ["k4mb1_motel1_shell"] = vec4(-0.15, -0.08, 0.99, 270.31),
    -- ["k4mb1_motel2_shell"] = vec4(-0.21, -0.05, 0.99, 270.31),

    -- -----2022sep-----
    -- ["gunworkshop_k4mb1"] = vec4(0.15, 4.67, -0.81, 179.08),
    -- ["safehouse_k4mb1"] = vec4(-4.99, 1.06, -0.83, 269.85),
    -- ["warehouse_k4mb1"] = vec4(-13.2, -0.03, -2.07, 271.0),
    -- ["luxury_housing1_k4mb1"] = vec4(-6.09, -1.06, -0.7, 269.76),
    -- ["luxury_housing2_k4mb1"] = vec4(-6.02, -1.04, -0.7, 270.06),
    -- ["luxury_housing3_k4mb1"] = vec4(-5.88, -1.1, -0.7, 269.61),
    -- ["luxury_housing4_k4mb1"] = vec4(-6.03, -0.95, -0.7, 267.68),
    -- ["new_garages1_k4mb1"] = vec4(-0.15, 13.94, 3.46, 180.7),
    -- ["new_garages2_k4mb1"] = vec4(-3.57, -0.07, -0.69, 270.88),
    -- ["new_garages3_k4mb1"] = vec4(-3.45, -0.1, -0.69, 269.05),
    -- ["new_garages4_k4mb1"] = vec4(8.5, 1.6, -0.75, 88.41),
    -- ["manor_housing1_k4mb1"] = vec4(6.92, -8.62, -4.97, 358.82),

    -- -----2023january----
    -- ["k4mb1_house1_shell"] = vec4(-3.04, -4.53, -0.46, 359.39),
    -- ["k4mb1_house2_shell"] = vec4(-8.29, 1.13, -3.18, 271.37),
    -- ["k4mb1_house3_shell"] = vec4(9.1, -7.39, -2.79, 359.55),
    -- ["k4mb1_house4_shell"] = vec4(-2.21, -2.7, -1.99, 358.95),
    -- ["k4mb1_basement1_shell"] = vec4(-4.51, -4.7, 1.6, 90.06),
    -- ["k4mb1_basement2_shell"] = vec4(-4.56, -5.03, 1.6, 84.52),
    -- ["k4mb1_basement3_shell"] = vec4(-4.7, -4.7, 1.6, 82.92),
    -- ["k4mb1_basement4_shell"] = vec4(-4.55, -4.78, 1.6, 93.28),
    -- ["k4mb1_basement5_shell"] = vec4(-4.47, -4.99, 1.6, 92.14),
    -- ["k4mb1_casinohotel_shell"] = vec4(-2.88, -0.02, -0.58, 270.31),

    -- ----2023may----
    -- ["k4mb1_furnishedoffice1_shell"] = vec4(3.71, -2.06, -0.91, 359.39),
    -- ["k4mb1_furnishedoffice2_shell"] = vec4(4.57, 3.875, -074, 271.37),
    -- ["k4mb1_furnishedoffice3_shell"] = vec4(11.84, -23.8, -0.69, 359.55),
    -- ["k4mb1_furnishedoffice4_shell"] = vec4(8.93, -2.29, -1.54, 358.95),
    -- ["k4mb1_furnishedoffice5_shell"] = vec4(0.14, -14.58, -3.07, 90.06),
    -- ["k4mb1_hoodhouse1_shell"] = vec4(-2.204, -7.34, 1.0, 84.52),
    -- ["k4mb1_laundry_shell"] = vec4(10.45, -6.00, -2.38, 82.92),
    -- ["k4mb1_palhouse1_shell"] = vec4(-0.79, 5.50, -1.66, 93.28),
    -- ["k4mb1_sandyhouse1_shell"] = vec4(1.52, -4.99, 0.08, 92.14),
    -- ["kambi_emptyhouse1"] = vec4(-0.73, -2.25, 1.0, 270.31),
    -- ["kambi_furnishedhouse1"] = vec4(-0.72, -2.25, 1.0, 270.31),

    -- -------classichousing
    -- ["classichouse_shell"] = vec4(4.57, -2.12, -3.38, 92.01),
    -- ["classichouse2_shell"] = vec4(4.43, -1.94, -3.38, 93.76),
    -- ["classichouse3_shell"] = vec4(4.7, -2.13, -3.38, 86.95),

    -- -----deluxe housing
    -- ["shell_highend"] = vec4(-22.23, -0.55, 7.21, 272.48),
    -- ["shell_highendv2"] = vec4(-10.47, 0.93, 1.94, 271.68),

    -- -----EmptyHotelHousing
    -- ["k4_hotel1_shell"] = vec4(5.01, 4.02, -0.81, 181.75),
    -- ["k4_hotel2_shell"] = vec4(4.96, 4.3, -0.81, 182.2),
    -- ["k4_hotel3_shell"] = vec4(4.98, 4.02, -0.81, 181.08),

    -- -----EmptyMotelHousing
    -- ["k4_motel1_shell"] = vec4(-0.13, -2.4, -0.56, 270.27),
    -- ["k4_motel2_shell"] = vec4(0.02, -3.5, -0.34, 0.75),
    -- ["k4_motel3_shell"] = vec4(3.13, 3.17, -0.52, 179.55),

    -- -----FurnishedHighendLabs
    -- ["k4coke_shell"] = vec4(-10.84, -2.57, -0.07, 271.11),
    -- ["k4meth_shell"] = vec4(-10.7, -2.53, -0.07, 271.73),
    -- ["k4weed_shell"] = vec4(-10.38, -2.44, -0.07, 272.9),

    -- -----FurnishedHotels
    -- ["modernhotel2_shell"] = vec4(5.0, 3.98, -0.82, 179.32),
    -- ["modernhotel3_shell"] = vec4(5.0, 4.06, -0.82, 179.66),

    -- ----FurnishedHousing
    -- ["furnitured_motel"] = vec4(-1.47, -3.88, -0.36, 1.52),
    -- ["furnitured_lowapart"] = vec4(4.85, -1.32, 0.35, 358.71),

    -- ----FurnishedLabs
    -- ["shell_coke1"] = vec4(-6.08, 8.46, -0.96, 180.77),
    -- ["shell_coke2"] = vec4(-6.27, 8.24, -0.96, 183.24),
    -- ["shell_weed"] = vec4(17.49, 11.68, -2.1, 90.42),
    -- ["shell_weed2"] = vec4(17.54, 11.65, -2.1, 91.21),
    -- ["shell_meth"] = vec4(-6.37, 8.32, -0.96, 178.73),

    -- ----FurnishedMotels
    -- ["classicmotel_shell"] = vec4(-0.13, -3.43, -0.34, 2.03),
    -- ["highendmotel_shell"] = vec4(3.15, 3.16, -0.52, 179.57),

    -- -----FurnishedStashhouse
    -- ["container2_shell"] = vec4(0.2, -5.45, -0.21, 358.85),
    -- ["stashhouse1_shell"] = vec4(20.94, -0.34, -2.07, 89.2),
    -- ["stashhouse3_shell"] = vec4(-0.08, 5.27, -1.01, 183.13),

    -- -----HighendHousing
    -- ["shell_apartment1"] = vec4(-2.23, 8.84, 3.2, 181.91),
    -- ["shell_apartment2"] = vec4(-2.19, 8.67, 3.2, 181.45),
    -- ["shell_apartment3"] = vec4(11.59, 4.45, 2.01, 131.73),

    -- -----MansionHousing
    -- ["k4_mansion_shell"] = vec4(-0.32, -1.31, 1.01, 181.66),
    -- ["k4_mansion2_shell"] = vec4(-0.17, -1.15, 1.01, 179.68),
    -- ["k4_mansion3_shell"] = vec4(-0.09, -0.99, 1.01, 183.62),

    -- ----MediumHousing
    -- ["shell_medium2"] = vec4(6.05, 0.37, -0.66, 0.35),
    -- ["shell_medium3"] = vec4(-2.53, 7.67, 1.19, 179.5),

    -- ----ModernHousing
    -- ["shell_banham"] = vec4(-3.34, -1.53, 1.24, 89.52),
    -- ["shell_westons"] = vec4(4.2, 10.16, 1.35, 181.01),
    -- ["shell_westons2"] = vec4(-1.87, 10.23, 1.35, 181.21),

    -- ----ShellsGarage
    -- ["shell_garages"] = vec4(5.87, 3.7, -0.5, 175.23),
    -- ["shell_garagel"] = vec4(12.34, -14.41, -1.0, 89.64),

    -- -----ShellsOffice
    -- ["shell_office2"] = vec4(3.32, -1.84, -0.87, 91.4),
    -- ["shell_officebig"] = vec4(-9.08, -3.46, -0.4, 356.64),

    -- -----ShellsStashhouse
    -- ["stashhouse_shell"] = vec4(21.23, -0.37, -2.07, 90.47),
    -- ["stashhouse2_shell"] = vec4(-1.88, 2.24, -1.01, 269.18),

    -- ----ShellsStore
    -- ["shell_gunstore"] = vec4(-0.92, -5.04, -0.74, 1.97),
    -- ["shell_store2"] = vec4(-0.91, -4.92, -1.15, 359.12),
    -- ["shell_store3"] = vec4(0.13, -7.36, -0.3, 0.29),
    -- ["shell_barber"] = vec4(1.59, 5.23, -0.56, 180.83),

    -- -----ShellsWarehouse
    -- ["shell_warehouse2"] = vec4(-11.99, 5.54, -2.06, 270.33),
    -- ["shell_warehouse3"] = vec4(2.13, -1.65, -0.94, 87.0),

    -- -----V2DefaultHousing
    -- ["default_housing1_k4mb1"] = vec4(-2.11, -5.75, -0.32, 6.44),
    -- ["default_housing2_k4mb1"] = vec4(-5.11, -4.94, -0.6, 273.93),
    -- ["default_housing3_k4mb1"] = vec4(-1.4, -1.83, -0.48, 3.95),
    -- ["default_housing4_k4mb1"] = vec4(0.23, -3.36, -0.41, 2.23),
    -- ["default_housing5_k4mb1"] = vec4(1.43, -14.2, -0.49, 0.07),
    -- ["default_housing6_k4mb1"] = vec4(4.54, -6.48, -1.65, 355.85),

    -- ----V2DeluxeHousing
    -- ["deluxe_housing1_k4mb1"] = vec4(-22.3, -0.33, 7.21, 91.56),
    -- ["deluxe_housing2_k4mb1"] = vec4(-10.4, 0.88, 1.94, 274.3),
    -- ["deluxe_housing3_k4mb1"] = vec4(-9.48, 5.64, -4.06, 89.9),

    -- ----V2HighendHousing
    -- ["highend_housing1_k4mb1"] = vec4(-2.27, 8.98, 3.2, 182.25),
    -- ["highend_housing2_k4mb1"] = vec4(-2.26, 8.56, 3.2, 178.87),
    -- ["highend_housing3_k4mb1"] = vec4(11.37, 4.58, 2.01, 126.7),

    -- ----V2MediumHousing
    -- ["medium_housing1_k4mb1"] = vec4(-0.25, -5.67, -0.57, 0.14),
    -- ["medium_housing2_k4mb1"] = vec4(6.05, 0.47, -0.66, 3.76),
    -- ["medium_housing3_k4mb1"] = vec4(5.7, -1.79, 1.2, 92.66),

    -- ----V2ModernHousing
    -- ["modern_housing1_k4mb1"] = vec4(4.4, 10.26, 1.35, 182.63),
    -- ["modern_housing2_k4mb1"] = vec4(-1.69, 10.33, 1.35, 181.66),
    -- ["modern_housing3_k4mb1"] = vec4(-3.61, -1.6, 1.24, 95.09),

    -- ----VinewoodHousing
    -- ["vinewood_housing1_k4mb1"] = vec4(10.97, -3.14, -2.36, 181.55),
    -- ["vinewood_housing2_k4mb1"] = vec4(1.64, 4.75, 1.71, 179.59),
    -- ["vinewood_housing3_k4mb1"] = vec4(3.37, 6.63, -2.32, 177.76),

    -- Furry Housing Pack (18 Shells) Unfurnished
    ["skyview_shell_u"] = vec4(1.05, 3.10, 1.00, 179.92),
    ["jap_shell_u"] = vec4(4.83, 0.85, -0.83, 89.76),
    ["loft_shell"] = vec4(2.08, -8.30, 1.01, 359.24),
    ["midshell1_u"] = vec4(4.21, -3.62, 1.00, 358.43),
    ["midshell2_u"] = vec4(6.97, -8.06, 1.00, 358.99),
    ["cozy_shell_u"] = vec4(2.25, -8.28, 1.01, 359.20),
    ["fury_shell04"] = vec4(-17.07, -5.97, 1.80, 268.21),
    ["fury_shell06"] = vec4(-17.01, -6.03, 1.80, 268.81),
    ["fury_shell07"] = vec4(-3.49, 18.11, 1.41, 180.25),
    ["fury_shell08"] = vec4(-14.08, -2.00, 7.21, 269.65),
    ["fury_shell09"] = vec4(-7.72, -0.96, 1.60, 178.98),

    -- Furry Housing Pack (18 Shells) Furnished
    ["skyview_shell_f"] = vec4(1.05, 3.10, 1.00, 179.92),
    ["jap_shell_f"] = vec4(4.83, 0.85, -0.83, 89.76),
    ["midshell1_f"] = vec4(4.21, -3.62, 1.00, 358.43),
    ["midshell2_f"] = vec4(6.97, -8.06, 1.00, 358.99),
    ["cozy_shell_f"] = vec4(2.25, -8.28, 1.01, 359.20),

    -- Furry Housing Pack V2 Unfurnished
    ["fury_shell01_u"] = vec4(13.22, 11.80, -0.56, 92.57),
    ["fury_shell02_u"] = vec4(13.22, 11.80, -0.56, 92.57),
    ["fury_shell03_u"] = vec4(13.22, 11.80, -0.56, 92.57),
    ["fury_shell04_u"] = vec4(4.97, -1.18, 1.00, 179.27),
    ["fury_shell05_u"] = vec4(13.22, 11.80, -0.56, 92.57),

    -- Furry Housing Pack V2 Furnished
    ["fury_shell01_f"] = vec4(13.22, 11.80, -0.56, 92.57),
    ["fury_shell02_f"] = vec4(13.22, 11.80, -0.56, 92.57),
    ["fury_shell03_f"] = vec4(13.22, 11.80, -0.56, 92.57),
    ["fury_shell04_f"] = vec4(4.93, -1.18, 1.00, 179.27),
    ["fury_shell05_f"] = vec4(-1.42, -4.82, 1.00, 359.63),
}
